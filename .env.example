# Production Environment Variables Template
# Copy this file to .env and fill in your Firebase configuration

# Firebase Configuration for shinesolution-a372c
VITE_FIREBASE_API_KEY=your-api-key-here
VITE_FIREBASE_AUTH_DOMAIN=shinesolution-a372c.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=shinesolution-a372c
VITE_FIREBASE_STORAGE_BUCKET=shinesolution-a372c.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
VITE_FIREBASE_APP_ID=your-app-id
VITE_FIREBASE_MEASUREMENT_ID=your-measurement-id

# Application Configuration
VITE_APP_NAME="Ottawa Shine Solutions"
VITE_APP_VERSION="1.3.0"
VITE_APP_ENVIRONMENT="production"

# Contact Information
VITE_COMPANY_EMAIL="<EMAIL>"
VITE_COMPANY_PHONE="(*************"

# Google Analytics Configuration
# Replace with your actual Google Analytics 4 measurement ID
# You can find this in your Google Analytics property settings
# Format: G-XXXXXXXXXX
VITE_GA_MEASUREMENT_ID=GA_MEASUREMENT_ID

# Admin Configuration
# Email addresses that should have admin access (comma-separated)
VITE_ADMIN_EMAILS=<EMAIL>,<EMAIL>
VITE_COMPANY_ADDRESS="Ottawa, ON, Canada"

# Features
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DISCOUNT_CODES=true
