# Ottawa Shine Solutions - Deployment Guide

## 🚀 Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Firebase project created
- [ ] Firebase CLI installed (`npm install -g firebase-tools`)
- [ ] Firebase CLI logged in (`firebase login`)
- [ ] Project initialized (`firebase use <project-id>`)

### 2. Configuration Files
- [ ] `.env` file created from `.env.example` with actual Firebase config
- [ ] `firestore.rules` updated with admin email addresses
- [ ] `firebase.json` configured for hosting
- [ ] All sensitive data in environment variables

### 3. Build & Test
- [ ] Dependencies installed (`npm install`)
- [ ] TypeScript compiles without errors (`npm run build`)
- [ ] Linting passes (`npm run lint`)
- [ ] Preview works locally (`npm run preview`)

### 4. Firebase Services
- [ ] Firestore database created
- [ ] Authentication enabled (Google provider)
- [ ] Hosting enabled
- [ ] Security rules deployed

## 📋 Deployment Steps

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your Firebase configuration
# Get values from Firebase Console > Project Settings
```

### Step 3: Build the Application
```bash
npm run build
```

### Step 4: Deploy to Firebase
```bash
# Deploy everything (hosting + firestore rules)
npm run deploy

# OR deploy separately
npm run deploy:hosting  # Just the website
npm run deploy:rules    # Just the security rules
```

### Step 5: Verify Deployment
```bash
# Preview locally before deploying
npm run check-build

# After deployment, test:
# - Website loads correctly
# - Quote form works
# - Contact form works
# - Discount codes work
# - Admin login works
```

## 🔧 Firebase Console Setup

### 1. Firestore Database
1. Go to Firebase Console > Firestore Database
2. Create database in production mode
3. Deploy security rules: `npm run deploy:rules`

### 2. Authentication
1. Go to Authentication > Sign-in method
2. Enable Google provider
3. Add authorized domains if needed

### 3. Hosting
1. Hosting will be configured automatically with deployment
2. Custom domain can be added in Hosting section

### 4. Admin Users
Update admin emails in:
- `firestore.rules` (line 9-13)
- `src/services/firebase.ts` (line 118-122)

## 📊 Post-Deployment

### Testing
- [ ] Homepage loads
- [ ] Navigation works
- [ ] Services page displays correctly
- [ ] Quote form submits successfully
- [ ] Contact form submits successfully
- [ ] Discount codes can be applied
- [ ] Admin can log in and see submissions
- [ ] Mobile responsive design works

### Monitoring
- Firebase Console > Analytics
- Firebase Console > Firestore > Usage
- Firebase Console > Hosting > Usage

### SEO & Performance
- [ ] Meta tags are properly set
- [ ] Images are optimized
- [ ] Lazy loading is working
- [ ] Site loads quickly

## 🛠️ Troubleshooting

### Common Issues

**Build Errors:**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npm run build
```

**Firebase Permission Errors:**
```bash
# Re-login and set project
firebase login
firebase use <project-id>
```

**Environment Variables Not Working:**
- Ensure `.env` file is in root directory
- Variables must start with `VITE_`
- Restart dev server after changes

**Firestore Permission Denied:**
- Check security rules are deployed
- Verify admin email addresses
- Test rules in Firebase Console

## 📈 Optimization for Production

### Performance
- Images are optimized and properly sized
- Code splitting with React.lazy()
- Caching headers configured in firebase.json
- Minimal bundle size

### Security
- Environment variables for sensitive data
- Firestore security rules implemented
- HTTPS enforced by Firebase Hosting
- Security headers configured

### SEO
- Proper meta tags in index.html
- Semantic HTML structure
- Fast loading times
- Mobile-first responsive design

## 🎯 Next Steps After Deployment

1. **Custom Domain** (Optional)
   - Add custom domain in Firebase Hosting
   - Configure DNS records

2. **Analytics**
   - Set up Google Analytics if needed
   - Monitor user behavior and conversions

3. **Backup Strategy**
   - Export Firestore data regularly
   - Keep environment variables secure

4. **Updates**
   - Use version control for updates
   - Test changes locally before deploying
   - Use `npm run preview` to test builds

## 📞 Support

If you encounter issues:
1. Check Firebase Console for error logs
2. Review Firestore security rules
3. Verify environment variables
4. Test locally with `npm run dev`

---

**Version:** 1.0.0  
**Last Updated:** June 23, 2025  
**Framework:** React + TypeScript + Vite + Firebase
