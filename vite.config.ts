import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    // Enable code splitting for better lazy loading
    rollupOptions: {
      output: {
        manualChunks: {
          // Separate vendor chunks for better caching
          vendor: ['react', 'react-dom'],
          firebase: ['firebase/app', 'firebase/firestore'],
          ui: ['lucide-react']
        }
      }
    },
    // Improve chunk size warnings threshold
    chunkSizeWarningLimit: 1000
  },
  // Optimize deps for faster dev server
  optimizeDeps: {
    include: [
      'react', 
      'react-dom', 
      'lucide-react',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore'
    ],
    exclude: ['firebase']
  }
})
