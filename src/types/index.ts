import type { LucideIcon } from 'lucide-react';

export interface Service {
  id: string;
  name: string;
  icon: LucideIcon;
  description: string;
  features: string[];
  basePrice: number;
}

export interface QuoteFormData {
  name: string;
  email: string;
  phone: string;
  serviceType: string;
  propertySize: string;
  frequency: string;
  additionalServices: string[];
  message: string;
  paymentMethod: 'fixed' | 'hourly';
  supplyMethod?: 'customer' | 'company';
  selectedHours?: number;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

export interface PricingResult {
  subtotal: number;
  discountAmount: number;
  total: number;
  isHourly?: boolean;
  hourlyRate?: number;
  estimatedHours?: number;
}

export interface DiscountCode {
  percentage: number;
  active: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface DiscountCodeWithId extends DiscountCode {
  id: string;
  code: string;
}

export interface AdminAnalytics {
  totalQuotes: number;
  totalContacts: number;
  pendingQuotes: number;
  newContacts: number;
  totalRevenue: number;
}

export type PropertySize = 'small' | 'medium' | 'large' | 'xlarge';
export type CleaningFrequency = 'one-time' | 'weekly' | 'bi-weekly' | 'monthly';
export type Page = 'home' | 'services' | 'quote' | 'contact' | 'admin' | 'login';

export interface QuoteSubmission extends QuoteFormData {
  pricing?: PricingResult;
  discountCode?: string | null;
  timestamp?: string;
  status?: string;
}
