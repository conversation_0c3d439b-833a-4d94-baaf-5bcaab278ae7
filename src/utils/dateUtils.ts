// Utility functions for handling dates in Firebase context

export interface FirebaseTimestamp {
  toDate: () => Date;
  seconds: number;
  nanoseconds: number;
}

/**
 * Safely convert various timestamp formats to a JavaScript Date
 */
export const safeParseDate = (timestamp: any): Date | null => {
  if (!timestamp) return null;
  
  try {
    // Handle Firestore Timestamp objects
    if (timestamp && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }
    
    // Handle Firestore Timestamp-like objects with seconds
    if (timestamp && typeof timestamp === 'object' && 'seconds' in timestamp) {
      return new Date(timestamp.seconds * 1000);
    }
    
    // Handle ISO strings and regular dates
    const date = new Date(timestamp);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return null;
    }
    
    return date;
  } catch (error) {
    console.warn('Error parsing date:', timestamp, error);
    return null;
  }
};

/**
 * Format a date for display in the admin panel
 */
export const formatDisplayDate = (timestamp: any, options?: Intl.DateTimeFormatOptions): string => {
  const date = safeParseDate(timestamp);
  
  if (!date) {
    return 'Invalid date';
  }
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  
  return date.toLocaleDateString('en-US', options || defaultOptions);
};

/**
 * Format a date for simple display (date only)
 */
export const formatSimpleDate = (timestamp: any): string => {
  const date = safeParseDate(timestamp);
  
  if (!date) {
    return 'Invalid date';
  }
  
  return date.toLocaleDateString();
};
