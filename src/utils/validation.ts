import type { QuoteFormData, ContactFormData } from '../types';
import { hourlyServices } from './services';

export interface ValidationError {
  [key: string]: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError;
}

export const validateQuoteForm = (formData: Partial<QuoteFormData>): ValidationResult => {
  const errors: ValidationError = {};
  const isHourlyService = formData.serviceType && hourlyServices[formData.serviceType as keyof typeof hourlyServices];

  // Name validation
  if (!formData.name?.trim()) {
    errors.name = 'Full name is required';
  } else if (formData.name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters';
  }

  // Email validation
  if (!formData.email?.trim()) {
    errors.email = 'Email address is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
    errors.email = 'Please enter a valid email address';
  }

  // Phone validation
  if (!formData.phone?.trim()) {
    errors.phone = 'Phone number is required';
  } else {
    // Remove all non-digit characters and check if we have at least 10 digits
    const digitsOnly = formData.phone.replace(/\D/g, '');
    if (digitsOnly.length < 10) {
      errors.phone = 'Please enter a valid phone number';
    }
  }

  // Service type validation
  if (!formData.serviceType) {
    errors.serviceType = 'Please select a service type';
  }

  // Property size validation (only required for non-hourly services)
  if (!isHourlyService && !formData.propertySize) {
    errors.propertySize = 'Please select property size';
  }

  // Hours validation (only required for hourly services)
  if (isHourlyService && !formData.selectedHours) {
    errors.selectedHours = 'Please select number of hours';
  } else if (isHourlyService && formData.selectedHours && formData.selectedHours < isHourlyService.minHours) {
    errors.selectedHours = `Minimum ${isHourlyService.minHours} hours required`;
  }

  // Frequency validation
  if (!formData.frequency) {
    errors.frequency = 'Please select cleaning frequency';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

export const validateContactForm = (formData: Partial<ContactFormData>): ValidationResult => {
  const errors: ValidationError = {};

  // Name validation
  if (!formData.name?.trim()) {
    errors.name = 'Full name is required';
  } else if (formData.name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters';
  }

  // Email validation
  if (!formData.email?.trim()) {
    errors.email = 'Email address is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
    errors.email = 'Please enter a valid email address';
  }

  // Phone validation (optional for contact form)
  if (formData.phone?.trim() && !/^[\d\s\-()+']{10,}$/.test(formData.phone.replace(/\s/g, ''))) {
    errors.phone = 'Please enter a valid phone number';
  }

  // Subject validation
  if (!formData.subject) {
    errors.subject = 'Please select a subject';
  }

  // Message validation
  if (!formData.message?.trim()) {
    errors.message = 'Message is required';
  } else if (formData.message.trim().length < 10) {
    errors.message = 'Message must be at least 10 characters';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
