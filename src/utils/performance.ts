// Performance monitoring utilities
export const performanceUtils = {
  // Track component loading times
  trackComponentLoad: (componentName: string) => {
    if (typeof window !== 'undefined' && window.performance) {
      const mark = `${componentName}-start`;
      performance.mark(mark);
      
      return () => {
        const endMark = `${componentName}-end`;
        performance.mark(endMark);
        performance.measure(componentName, mark, endMark);
        
        // Log performance data in development
        if (import.meta.env.DEV) {
          const entries = performance.getEntriesByName(componentName);
          const lastEntry = entries[entries.length - 1];
          console.log(`Component ${componentName} loaded in ${lastEntry.duration.toFixed(2)}ms`);
        }
      };
    }
    return () => {};
  },

  // Track lazy loading effectiveness
  trackLazyLoad: (chunkName: string) => {
    if (import.meta.env.DEV) {
      console.log(`Lazy loading chunk: ${chunkName}`);
    }
  }
};
