import { describe, it, expect } from 'vitest';
import { pricingCalculator } from '../pricing';
import type { PropertySize, CleaningFrequency } from '../../types';

describe('Pricing Calculator', () => {
  describe('Fixed Pricing Services', () => {
    it('should calculate basic house cleaning price', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        150
      );

      expect(result).toEqual({
        subtotal: 150,
        discountAmount: 0,
        total: 150,
        isHourly: false
      });
    });

    it('should apply property size multipliers correctly', () => {
      const testCases = [
        { size: 'small' as PropertySize, expected: 150 },
        { size: 'medium' as PropertySize, expected: 225 },
        { size: 'large' as PropertySize, expected: 300 },
        { size: 'xlarge' as PropertySize, expected: 450 }
      ];

      testCases.forEach(({ size, expected }) => {
        const result = pricingCalculator.calculate(
          'house',
          size,
          'one-time' as CleaningFrequency,
          [],
          0,
          150
        );
        expect(result.subtotal).toBe(expected);
      });
    });

    it('should apply frequency discounts correctly', () => {
      const testCases = [
        { frequency: 'one-time' as CleaningFrequency, expected: 150 },
        { frequency: 'weekly' as CleaningFrequency, expected: 128 }, // 15% discount
        { frequency: 'bi-weekly' as CleaningFrequency, expected: 135 }, // 10% discount
        { frequency: 'monthly' as CleaningFrequency, expected: 143 } // 5% discount
      ];

      testCases.forEach(({ frequency, expected }) => {
        const result = pricingCalculator.calculate(
          'house',
          'small' as PropertySize,
          frequency,
          [],
          0,
          150
        );
        expect(result.subtotal).toBe(expected);
      });
    });

    it('should add additional services cost', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        ['Deep cleaning', 'Carpet cleaning'],
        0,
        150
      );

      expect(result.subtotal).toBe(200); // 150 + (2 * 25)
    });

    it('should apply discount percentage', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        10, // 10% discount
        150
      );

      expect(result.discountAmount).toBe(15);
      expect(result.total).toBe(135);
    });

    it('should handle complex calculation with all factors', () => {
      const result = pricingCalculator.calculate(
        'office',
        'large' as PropertySize, // 2x multiplier
        'weekly' as CleaningFrequency, // 0.85 multiplier
        ['Deep cleaning', 'Window cleaning'], // +50
        15, // 15% discount
        200
      );

      // Base: 200 * 2 * 0.85 = 340
      // Additional: 2 * 25 = 50
      // Subtotal: 340 + 50 = 390
      // Discount: 390 * 0.15 = 58.5 (rounded to 59)
      // Total: 390 - 59 = 331
      expect(result.subtotal).toBe(390);
      expect(result.discountAmount).toBe(59);
      expect(result.total).toBe(331);
    });
  });

  describe('Hourly Pricing Services', () => {
    it('should calculate customer supply hourly service', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize, // Not used for hourly
        'one-time' as CleaningFrequency,
        [],
        0,
        25, // Base price (hourly rate)
        3 // Selected hours
      );

      expect(result).toEqual({
        subtotal: 75, // 25 * 3
        discountAmount: 0,
        total: 75,
        isHourly: true,
        hourlyRate: 25,
        estimatedHours: 3
      });
    });

    it('should calculate company supply hourly service', () => {
      const result = pricingCalculator.calculate(
        'hourly-company-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        35,
        4
      );

      expect(result.subtotal).toBe(140); // 35 * 4
      expect(result.hourlyRate).toBe(35);
      expect(result.estimatedHours).toBe(4);
    });

    it('should calculate backyard hourly service', () => {
      const result = pricingCalculator.calculate(
        'backyard-hourly',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        30,
        5
      );

      expect(result.subtotal).toBe(150); // 30 * 5
      expect(result.hourlyRate).toBe(30);
      expect(result.estimatedHours).toBe(5);
    });

    it('should use minimum hours when selectedHours is not provided', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        25
        // No selectedHours provided
      );

      expect(result.estimatedHours).toBe(2); // Minimum hours
      expect(result.subtotal).toBe(50); // 25 * 2
    });

    it('should apply frequency discounts to hourly rates', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize,
        'weekly' as CleaningFrequency, // 15% discount
        [],
        0,
        25,
        3
      );

      expect(result.hourlyRate).toBe(21.25); // 25 * 0.85
      expect(result.subtotal).toBe(64); // Math.round(21.25 * 3)
    });

    it('should add additional services with reduced rate for hourly', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        ['Deep cleaning', 'Window cleaning'],
        0,
        25,
        3
      );

      expect(result.subtotal).toBe(105); // (25 * 3) + (2 * 15)
    });

    it('should apply discount to hourly service total', () => {
      const result = pricingCalculator.calculate(
        'hourly-customer-supply',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        20, // 20% discount
        25,
        4
      );

      expect(result.subtotal).toBe(100);
      expect(result.discountAmount).toBe(20);
      expect(result.total).toBe(80);
    });

    it('should handle complex hourly calculation', () => {
      const result = pricingCalculator.calculate(
        'hourly-company-supply',
        'medium' as PropertySize, // Not used for hourly
        'bi-weekly' as CleaningFrequency, // 10% discount
        ['Deep cleaning'],
        10, // 10% discount code
        35,
        6
      );

      // Hourly rate: 35 * 0.9 = 31.5
      // Base cost: 31.5 * 6 = 189
      // Additional: 1 * 15 = 15
      // Subtotal: 189 + 15 = 204
      // Discount: 204 * 0.1 = 20.4 (rounded to 20)
      // Total: 204 - 20 = 184
      expect(result.hourlyRate).toBe(31.5);
      expect(result.subtotal).toBe(204);
      expect(result.discountAmount).toBe(20);
      expect(result.total).toBe(184);
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero additional services', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        150
      );

      expect(result.subtotal).toBe(150);
    });

    it('should handle zero discount', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        0,
        150
      );

      expect(result.discountAmount).toBe(0);
      expect(result.total).toBe(150);
    });

    it('should round monetary values correctly', () => {
      const result = pricingCalculator.calculate(
        'house',
        'medium' as PropertySize, // 1.5 multiplier
        'weekly' as CleaningFrequency, // 0.85 multiplier
        [],
        0,
        100
      );

      // 100 * 1.5 * 0.85 = 127.5, should round to 128
      expect(result.subtotal).toBe(128);
    });

    it('should handle large discount percentages', () => {
      const result = pricingCalculator.calculate(
        'house',
        'small' as PropertySize,
        'one-time' as CleaningFrequency,
        [],
        50, // 50% discount
        200
      );

      expect(result.discountAmount).toBe(100);
      expect(result.total).toBe(100);
    });
  });
});
