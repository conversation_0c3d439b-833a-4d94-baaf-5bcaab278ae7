import { describe, it, expect } from 'vitest';
import { validateQuoteForm, validateContactForm } from '../validation';
import type { QuoteFormData, ContactFormData } from '../../types';

describe('Form Validation', () => {
  describe('Quote Form Validation', () => {
    const validQuoteData: Partial<QuoteFormData> = {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      serviceType: 'house',
      propertySize: 'small',
      frequency: 'weekly',
      additionalServices: [],
      message: 'Test message',
      paymentMethod: 'fixed'
    };

    describe('Name Validation', () => {
      it('should require name', () => {
        const result = validateQuoteForm({ ...validQuoteData, name: '' });
        expect(result.isValid).toBe(false);
        expect(result.errors.name).toBe('Full name is required');
      });

      it('should require name with whitespace only', () => {
        const result = validateQuoteForm({ ...validQuoteData, name: '   ' });
        expect(result.isValid).toBe(false);
        expect(result.errors.name).toBe('Full name is required');
      });

      it('should require minimum 2 characters', () => {
        const result = validateQuoteForm({ ...validQuoteData, name: 'A' });
        expect(result.isValid).toBe(false);
        expect(result.errors.name).toBe('Name must be at least 2 characters');
      });

      it('should accept valid names', () => {
        const validNames = ['John Doe', 'Jane Smith-Johnson', 'Ali', 'María José'];
        validNames.forEach(name => {
          const result = validateQuoteForm({ ...validQuoteData, name });
          expect(result.errors.name).toBeUndefined();
        });
      });
    });

    describe('Email Validation', () => {
      it('should require email', () => {
        const result = validateQuoteForm({ ...validQuoteData, email: '' });
        expect(result.isValid).toBe(false);
        expect(result.errors.email).toBe('Email address is required');
      });

      it('should validate email format', () => {
        const invalidEmails = [
          'invalid-email',
          '@example.com',
          'user@',
          'user.example.com',
          'user@.com',
          'user@example.',
          'user <EMAIL>'
        ];

        invalidEmails.forEach(email => {
          const result = validateQuoteForm({ ...validQuoteData, email });
          expect(result.isValid).toBe(false);
          expect(result.errors.email).toBe('Please enter a valid email address');
        });
      });

      it('should accept valid emails', () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>'
        ];

        validEmails.forEach(email => {
          const result = validateQuoteForm({ ...validQuoteData, email });
          expect(result.errors.email).toBeUndefined();
        });
      });
    });

    describe('Phone Validation', () => {
      it('should require phone number', () => {
        const result = validateQuoteForm({ ...validQuoteData, phone: '' });
        expect(result.isValid).toBe(false);
        expect(result.errors.phone).toBe('Phone number is required');
      });

      it('should validate phone format', () => {
        const invalidPhones = [
          '123',
          '123-456',
          'abcd-efgh-ijkl',
          '123-456-789a'
        ];

        invalidPhones.forEach(phone => {
          const result = validateQuoteForm({ ...validQuoteData, phone });
          expect(result.isValid).toBe(false);
          expect(result.errors.phone).toBe('Please enter a valid phone number');
        });
      });

      it('should accept valid phone formats', () => {
        const validPhones = [
          '(*************',
          '************',
          '5551234567',
          '****** 123 4567',
          '************'
        ];

        validPhones.forEach(phone => {
          const result = validateQuoteForm({ ...validQuoteData, phone });
          expect(result.errors.phone).toBeUndefined();
        });
      });
    });

    describe('Service Type Validation', () => {
      it('should require service type', () => {
        const result = validateQuoteForm({ ...validQuoteData, serviceType: '' });
        expect(result.isValid).toBe(false);
        expect(result.errors.serviceType).toBe('Please select a service type');
      });

      it('should accept valid service types', () => {
        const validServices = ['house', 'office', 'hotel', 'hourly-customer-supply', 'hourly-company-supply', 'backyard-hourly'];
        validServices.forEach(serviceType => {
          const result = validateQuoteForm({ ...validQuoteData, serviceType });
          expect(result.errors.serviceType).toBeUndefined();
        });
      });
    });

    describe('Property Size Validation for Fixed Services', () => {
      it('should require property size for non-hourly services', () => {
        const result = validateQuoteForm({ 
          ...validQuoteData, 
          serviceType: 'house',
          propertySize: '' 
        });
        expect(result.isValid).toBe(false);
        expect(result.errors.propertySize).toBe('Please select property size');
      });

      it('should not require property size for hourly services', () => {
        const result = validateQuoteForm({ 
          ...validQuoteData, 
          serviceType: 'hourly-customer-supply',
          propertySize: '',
          selectedHours: 3
        });
        expect(result.errors.propertySize).toBeUndefined();
      });

      it('should accept valid property sizes', () => {
        const validSizes = ['small', 'medium', 'large', 'xlarge'];
        validSizes.forEach(propertySize => {
          const result = validateQuoteForm({ ...validQuoteData, propertySize });
          expect(result.errors.propertySize).toBeUndefined();
        });
      });
    });

    describe('Hours Validation for Hourly Services', () => {
      it('should require hours for hourly services', () => {
        const result = validateQuoteForm({ 
          ...validQuoteData, 
          serviceType: 'hourly-customer-supply',
          selectedHours: undefined
        });
        expect(result.isValid).toBe(false);
        expect(result.errors.selectedHours).toBe('Please select number of hours');
      });

      it('should validate minimum hours for hourly services', () => {
        const result = validateQuoteForm({ 
          ...validQuoteData, 
          serviceType: 'hourly-customer-supply',
          selectedHours: 1 // Below minimum of 2
        });
        expect(result.isValid).toBe(false);
        expect(result.errors.selectedHours).toBe('Minimum 2 hours required');
      });

      it('should accept valid hours for hourly services', () => {
        const validHours = [2, 3, 4, 5, 8, 10];
        validHours.forEach(selectedHours => {
          const result = validateQuoteForm({ 
            ...validQuoteData, 
            serviceType: 'hourly-customer-supply',
            selectedHours
          });
          expect(result.errors.selectedHours).toBeUndefined();
        });
      });

      it('should not require hours for non-hourly services', () => {
        const result = validateQuoteForm({ 
          ...validQuoteData, 
          serviceType: 'house',
          selectedHours: undefined
        });
        expect(result.errors.selectedHours).toBeUndefined();
      });
    });

    describe('Frequency Validation', () => {
      it('should require frequency', () => {
        const result = validateQuoteForm({ ...validQuoteData, frequency: '' });
        expect(result.isValid).toBe(false);
        expect(result.errors.frequency).toBe('Please select cleaning frequency');
      });

      it('should accept valid frequencies', () => {
        const validFrequencies = ['one-time', 'weekly', 'bi-weekly', 'monthly'];
        validFrequencies.forEach(frequency => {
          const result = validateQuoteForm({ ...validQuoteData, frequency });
          expect(result.errors.frequency).toBeUndefined();
        });
      });
    });

    describe('Complete Form Validation', () => {
      it('should pass validation for complete fixed service form', () => {
        const result = validateQuoteForm(validQuoteData);
        expect(result.isValid).toBe(true);
        expect(Object.keys(result.errors)).toHaveLength(0);
      });

      it('should pass validation for complete hourly service form', () => {
        const hourlyData = {
          ...validQuoteData,
          serviceType: 'hourly-customer-supply',
          propertySize: '', // Not required for hourly
          selectedHours: 3
        };
        const result = validateQuoteForm(hourlyData);
        expect(result.isValid).toBe(true);
        expect(Object.keys(result.errors)).toHaveLength(0);
      });

      it('should collect multiple validation errors', () => {
        const invalidData = {
          name: '',
          email: 'invalid-email',
          phone: '123',
          serviceType: '',
          propertySize: '',
          frequency: ''
        };
        const result = validateQuoteForm(invalidData);
        expect(result.isValid).toBe(false);
        expect(Object.keys(result.errors)).toContain('name');
        expect(Object.keys(result.errors)).toContain('email');
        expect(Object.keys(result.errors)).toContain('phone');
        expect(Object.keys(result.errors)).toContain('serviceType');
        expect(Object.keys(result.errors)).toContain('frequency');
      });
    });
  });

  describe('Contact Form Validation', () => {
    const validContactData: Partial<ContactFormData> = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '(*************',
      subject: 'general',
      message: 'This is a test message with enough characters.'
    };

    describe('Basic Field Validation', () => {
      it('should require all mandatory fields', () => {
        const result = validateContactForm({
          name: '',
          email: '',
          subject: '',
          message: ''
        });
        expect(result.isValid).toBe(false);
        expect(result.errors.name).toBe('Full name is required');
        expect(result.errors.email).toBe('Email address is required');
        expect(result.errors.subject).toBe('Please select a subject');
        expect(result.errors.message).toBe('Message is required');
      });

      it('should validate message minimum length', () => {
        const result = validateContactForm({ 
          ...validContactData, 
          message: 'Too short' 
        });
        expect(result.isValid).toBe(false);
        expect(result.errors.message).toBe('Message must be at least 10 characters');
      });

      it('should make phone optional but validate if provided', () => {
        // Valid without phone
        const resultWithoutPhone = validateContactForm({ 
          ...validContactData, 
          phone: '' 
        });
        expect(resultWithoutPhone.errors.phone).toBeUndefined();

        // Invalid phone format
        const resultWithInvalidPhone = validateContactForm({ 
          ...validContactData, 
          phone: '123' 
        });
        expect(resultWithInvalidPhone.isValid).toBe(false);
        expect(resultWithInvalidPhone.errors.phone).toBe('Please enter a valid phone number');
      });

      it('should pass validation for complete contact form', () => {
        const result = validateContactForm(validContactData);
        expect(result.isValid).toBe(true);
        expect(Object.keys(result.errors)).toHaveLength(0);
      });
    });
  });

  describe('Validation Edge Cases', () => {
    it('should handle undefined form data', () => {
      const result = validateQuoteForm({});
      expect(result.isValid).toBe(false);
      expect(result.errors.name).toBeDefined();
      expect(result.errors.email).toBeDefined();
      expect(result.errors.phone).toBeDefined();
    });

    it('should trim whitespace in validations', () => {
      const result = validateQuoteForm({
        name: '  John Doe  ',
        email: '  <EMAIL>  ',
        phone: '  ************  ',
        serviceType: 'house',
        propertySize: 'small',
        frequency: 'weekly',
        message: '  Test message  '
      });
      expect(result.errors.name).toBeUndefined();
      expect(result.errors.email).toBeUndefined();
      expect(result.errors.phone).toBeUndefined();
    });

    it('should handle special characters in names', () => {
      const specialNames = [
        "O'Connor",
        "Jean-Pierre",
        "María José",
        "Smith Jr.",
        "Van Der Berg"
      ];

      specialNames.forEach(name => {
        const result = validateQuoteForm({
          name,
          email: '<EMAIL>',
          phone: '************',
          serviceType: 'house',
          propertySize: 'small',
          frequency: 'weekly'
        });
        expect(result.errors.name).toBeUndefined();
      });
    });
  });
});
