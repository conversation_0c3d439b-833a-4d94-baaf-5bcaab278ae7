import { describe, it, expect } from 'vitest';
import { serviceConfig, hourlyServices } from '../services';

describe('Services Configuration', () => {
  describe('Service Config', () => {
    it('should have all required services', () => {
      expect(serviceConfig).toHaveLength(6);
      
      const serviceIds = serviceConfig.map(service => service.id);
      expect(serviceIds).toContain('house');
      expect(serviceIds).toContain('office');
      expect(serviceIds).toContain('hotel');
      expect(serviceIds).toContain('hourly-customer-supply');
      expect(serviceIds).toContain('hourly-company-supply');
      expect(serviceIds).toContain('backyard-hourly');
    });

    it('should have valid service structure', () => {
      serviceConfig.forEach(service => {
        expect(service).toHaveProperty('id');
        expect(service).toHaveProperty('name');
        expect(service).toHaveProperty('icon');
        expect(service).toHaveProperty('description');
        expect(service).toHaveProperty('features');
        expect(service).toHaveProperty('basePrice');

        expect(typeof service.id).toBe('string');
        expect(typeof service.name).toBe('string');
        expect(typeof service.description).toBe('string');
        expect(typeof service.basePrice).toBe('number');
        expect(Array.isArray(service.features)).toBe(true);
        expect(service.features.length).toBeGreaterThan(0);
      });
    });

    it('should have reasonable base prices', () => {
      const houseCleaning = serviceConfig.find(s => s.id === 'house');
      const officeCleaning = serviceConfig.find(s => s.id === 'office');
      const hotelCleaning = serviceConfig.find(s => s.id === 'hotel');

      expect(houseCleaning?.basePrice).toBe(150);
      expect(officeCleaning?.basePrice).toBe(200);
      expect(hotelCleaning?.basePrice).toBe(300);
    });

    it('should have correct hourly service prices', () => {
      const customerSupply = serviceConfig.find(s => s.id === 'hourly-customer-supply');
      const companySupply = serviceConfig.find(s => s.id === 'hourly-company-supply');
      const backyard = serviceConfig.find(s => s.id === 'backyard-hourly');

      expect(customerSupply?.basePrice).toBe(25);
      expect(companySupply?.basePrice).toBe(35);
      expect(backyard?.basePrice).toBe(30);
    });

    it('should have descriptive names', () => {
      serviceConfig.forEach(service => {
        expect(service.name.length).toBeGreaterThan(5);
        expect(service.description.length).toBeGreaterThan(10);
      });
    });

    it('should have meaningful features', () => {
      serviceConfig.forEach(service => {
        service.features.forEach(feature => {
          expect(typeof feature).toBe('string');
          expect(feature.length).toBeGreaterThan(3);
        });
      });
    });
  });

  describe('Hourly Services Configuration', () => {
    it('should have all hourly services defined', () => {
      const hourlyServiceIds = Object.keys(hourlyServices);
      expect(hourlyServiceIds).toContain('hourly-customer-supply');
      expect(hourlyServiceIds).toContain('hourly-company-supply');
      expect(hourlyServiceIds).toContain('backyard-hourly');
    });

    it('should have correct hourly service structure', () => {
      Object.entries(hourlyServices).forEach(([, config]) => {
        expect(config).toHaveProperty('rate');
        expect(config).toHaveProperty('minHours');
        expect(config).toHaveProperty('supplyMethod');

        expect(typeof config.rate).toBe('number');
        expect(typeof config.minHours).toBe('number');
        expect(['customer', 'company']).toContain(config.supplyMethod);
      });
    });

    it('should have correct rates and minimum hours', () => {
      expect(hourlyServices['hourly-customer-supply']).toEqual({
        rate: 25,
        minHours: 2,
        supplyMethod: 'customer'
      });

      expect(hourlyServices['hourly-company-supply']).toEqual({
        rate: 35,
        minHours: 2,
        supplyMethod: 'company'
      });

      expect(hourlyServices['backyard-hourly']).toEqual({
        rate: 30,
        minHours: 2,
        supplyMethod: 'company'
      });
    });

    it('should have reasonable minimum hours', () => {
      Object.values(hourlyServices).forEach(config => {
        expect(config.minHours).toBeGreaterThanOrEqual(2);
        expect(config.minHours).toBeLessThanOrEqual(4);
      });
    });

    it('should have logical pricing structure', () => {
      const customerSupply = hourlyServices['hourly-customer-supply'];
      const companySupply = hourlyServices['hourly-company-supply'];
      const backyard = hourlyServices['backyard-hourly'];

      // Company supply should be more expensive than customer supply
      expect(companySupply.rate).toBeGreaterThan(customerSupply.rate);
      
      // Backyard should be between customer and company supply
      expect(backyard.rate).toBeGreaterThan(customerSupply.rate);
      expect(backyard.rate).toBeLessThan(companySupply.rate);
    });
  });

  describe('Service Integration', () => {
    it('should have matching service configs and hourly services', () => {
      const hourlyServiceIds = Object.keys(hourlyServices);
      const configHourlyServices = serviceConfig
        .filter(service => hourlyServiceIds.includes(service.id))
        .map(service => service.id);

      expect(configHourlyServices).toEqual(hourlyServiceIds);
    });

    it('should have matching base prices with hourly rates', () => {
      Object.entries(hourlyServices).forEach(([id, hourlyConfig]) => {
        const service = serviceConfig.find(s => s.id === id);
        expect(service?.basePrice).toBe(hourlyConfig.rate);
      });
    });

    it('should identify fixed vs hourly services correctly', () => {
      const fixedServices = ['house', 'office', 'hotel'];
      const hourlyServiceIds = Object.keys(hourlyServices);

      fixedServices.forEach(id => {
        expect(hourlyServiceIds).not.toContain(id);
      });

      hourlyServiceIds.forEach(id => {
        expect(fixedServices).not.toContain(id);
      });
    });
  });

  describe('Service Content Quality', () => {
    it('should have unique service names', () => {
      const names = serviceConfig.map(s => s.name);
      const uniqueNames = [...new Set(names)];
      expect(names).toHaveLength(uniqueNames.length);
    });

    it('should have unique service descriptions', () => {
      const descriptions = serviceConfig.map(s => s.description);
      const uniqueDescriptions = [...new Set(descriptions)];
      expect(descriptions).toHaveLength(uniqueDescriptions.length);
    });

    it('should have relevant features for each service type', () => {
      const house = serviceConfig.find(s => s.id === 'house');
      const office = serviceConfig.find(s => s.id === 'office');
      const hotel = serviceConfig.find(s => s.id === 'hotel');

      expect(house?.features).toContain('Deep cleaning');
      expect(office?.features).toContain('Daily janitorial services');
      expect(hotel?.features).toContain('Room turnover');
    });

    it('should have appropriate features for hourly services', () => {
      const customerSupply = serviceConfig.find(s => s.id === 'hourly-customer-supply');
      const companySupply = serviceConfig.find(s => s.id === 'hourly-company-supply');
      const backyard = serviceConfig.find(s => s.id === 'backyard-hourly');

      expect(customerSupply?.features).toContain('Bring your own supplies');
      expect(companySupply?.features).toContain('Professional-grade supplies included');
      expect(backyard?.features).toContain('Patio cleaning');
    });
  });
});
