import type { PropertySize, CleaningFrequency, PricingResult } from '../types';
import { hourlyServices } from './services';

export const pricingCalculator = {
  sizeMultipliers: {
    'small': 1,
    'medium': 1.5,
    'large': 2,
    'xlarge': 3
  } as Record<PropertySize, number>,
  
  frequencyDiscounts: {
    'one-time': 1,
    'weekly': 0.85,
    'bi-weekly': 0.9,
    'monthly': 0.95
  } as Record<CleaningFrequency, number>,

  // Estimated hours based on property size for hourly services
  estimatedHours: {
    'small': 2,
    'medium': 3,
    'large': 4,
    'xlarge': 6
  } as Record<PropertySize, number>,
  
  calculate: (
    serviceType: string, 
    propertySize: PropertySize, 
    frequency: CleaningFrequency, 
    additionalServices: string[] = [], 
    discountPercentage: number = 0,
    basePrice: number = 80,
    selectedHours?: number
  ): PricingResult => {
    // Check if this is an hourly service
    const isHourlyService = hourlyServices[serviceType as keyof typeof hourlyServices];
    
    if (isHourlyService) {
      // Calculate hourly pricing
      const hourlyRate = isHourlyService.rate;
      const hours = selectedHours || isHourlyService.minHours;
      
      // Apply frequency discount to hourly rate
      const frequencyDiscount = pricingCalculator.frequencyDiscounts[frequency] || 1;
      const discountedHourlyRate = hourlyRate * frequencyDiscount;
      
      // Additional services for hourly (reduced rate)
      const additionalServicesPrice = additionalServices.length * 15; // Lower rate for hourly
      
      const subtotal = Math.round((discountedHourlyRate * hours) + additionalServicesPrice);
      const discountAmount = Math.round((subtotal * discountPercentage) / 100);
      
      return {
        subtotal,
        discountAmount,
        total: subtotal - discountAmount,
        isHourly: true,
        hourlyRate: discountedHourlyRate,
        estimatedHours: hours
      };
    } else {
      // Calculate fixed pricing (existing logic)
      const sizeMultiplier = pricingCalculator.sizeMultipliers[propertySize] || 1;
      const frequencyDiscount = pricingCalculator.frequencyDiscounts[frequency] || 1;
      const additionalServicesPrice = additionalServices.length * 25;
      
      const subtotal = (basePrice * sizeMultiplier * frequencyDiscount) + additionalServicesPrice;
      const discountAmount = Math.round((subtotal * discountPercentage) / 100);
      
      return {
        subtotal: Math.round(subtotal),
        discountAmount: discountAmount,
        total: Math.round(subtotal) - discountAmount,
        isHourly: false
      };
    }
  }
};
