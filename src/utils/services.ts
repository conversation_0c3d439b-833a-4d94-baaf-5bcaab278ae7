import { Home, Building, Hotel, Clock, TreePine } from 'lucide-react';
import type { Service } from '../types';

export const serviceConfig: Service[] = [
  {
    id: 'house',
    name: 'House Cleaning',
    icon: Home,
    description: 'Complete residential cleaning for your home',
    features: ['Deep cleaning', 'Regular maintenance', 'Move-in/out cleaning', 'Post-construction cleanup'],
    basePrice: 150
  },
  {
    id: 'office',
    name: 'Office Cleaning',
    icon: Building,
    description: 'Professional commercial cleaning for offices',
    features: ['Daily janitorial services', 'Carpet cleaning', 'Window cleaning', 'Sanitization'],
    basePrice: 200
  },
  {
    id: 'hotel',
    name: 'Hotel Cleaning',
    icon: Hotel,
    description: 'Specialized hospitality cleaning services',
    features: ['Room turnover', 'Lobby maintenance', 'Laundry services', '24/7 availability'],
    basePrice: 300
  },
  {
    id: 'hourly-customer-supply',
    name: 'Hourly Cleaning (Customer Supplies)',
    icon: Clock,
    description: 'Flexible hourly cleaning service with customer-provided supplies',
    features: ['Flexible scheduling', 'Bring your own supplies', 'Minimum 2 hours', 'Professional cleaners'],
    basePrice: 25
  },
  {
    id: 'hourly-company-supply',
    name: 'Hourly Cleaning (Company Supplies)',
    icon: Clock,
    description: 'Flexible hourly cleaning service with our professional supplies',
    features: ['Flexible scheduling', 'Professional-grade supplies included', 'Minimum 2 hours', 'Eco-friendly products'],
    basePrice: 35
  },
  {
    id: 'backyard-hourly',
    name: 'Backyard Cleaning (Hourly)',
    icon: TreePine,
    description: 'Outdoor cleaning and maintenance services',
    features: ['Patio cleaning', 'Deck maintenance', 'Outdoor furniture', 'Garden area cleanup'],
    basePrice: 30
  }
];

// Hourly service configuration
export const hourlyServices = {
  'hourly-customer-supply': {
    rate: 25,
    minHours: 2,
    supplyMethod: 'customer' as const
  },
  'hourly-company-supply': {
    rate: 35,
    minHours: 2,
    supplyMethod: 'company' as const
  },
  'backyard-hourly': {
    rate: 30,
    minHours: 2,
    supplyMethod: 'company' as const
  }
};
