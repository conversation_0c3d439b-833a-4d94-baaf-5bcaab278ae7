import React, { useState, useEffect, Suspense, lazy } from 'react';
import { CheckCircle, Calculator } from 'lucide-react';
import FormInput from '../components/FormInput';
import FormSelect from '../components/FormSelect';
import FormTextarea from '../components/FormTextarea';
import LoadingSpinner from '../components/LoadingSpinner';
import Toast from '../components/Toast';
import { useDiscountCode } from '../hooks/useDiscountCode';
import { useToast } from '../hooks/useToast';
import { pricingCalculator } from '../utils/pricing';
import { serviceConfig, hourlyServices } from '../utils/services';
import { firebaseService } from '../services/firebase';
import { analyticsService } from '../services/analytics';
import { validateQuoteForm } from '../utils/validation';
import type { QuoteFormData, QuoteSubmission, PricingResult } from '../types';
import type { ValidationError } from '../utils/validation';

// Lazy load the discount code input component
const DiscountCodeInput = lazy(() => import('../components/DiscountCodeInput'));

const QuotePage: React.FC = () => {
  const [formData, setFormData] = useState<QuoteFormData>({
    name: '',
    email: '',
    phone: '',
    serviceType: '',
    propertySize: '',
    frequency: '',
    additionalServices: [],
    message: '',
    paymentMethod: 'fixed'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [pricing, setPricing] = useState<PricingResult | null>(null);
  const [currentPricing, setCurrentPricing] = useState<PricingResult | null>(null);
  const [errors, setErrors] = useState<ValidationError>({});

  const { discount, isLoading, error, applyDiscountCode, clearDiscount } = useDiscountCode();
  const { toasts, showError, showSuccess, removeToast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const target = e.target as HTMLInputElement;
    
    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        additionalServices: target.checked 
          ? [...prev.additionalServices, value]
          : prev.additionalServices.filter(service => service !== value)
      }));
    } else if (name === 'selectedHours') {
      setFormData(prev => ({
        ...prev,
        [name]: value ? parseInt(value, 10) : undefined
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Calculate pricing when form data changes
  useEffect(() => {
    const isHourlyService = formData.serviceType && hourlyServices[formData.serviceType as keyof typeof hourlyServices];
    
    if (formData.serviceType && formData.frequency) {
      // For non-hourly services, require property size
      // For hourly services, require selected hours
      const canCalculate = isHourlyService 
        ? formData.selectedHours 
        : formData.propertySize;
        
      if (canCalculate) {
        const service = serviceConfig.find(s => s.id === formData.serviceType);
        if (service) {
          // For hourly services, use 'small' as default property size since it's not used in calculation
          const propertySize = isHourlyService ? 'small' : formData.propertySize;
          
          const result = pricingCalculator.calculate(
            formData.serviceType,
            propertySize as any,
            formData.frequency as any,
            formData.additionalServices,
            discount?.percentage || 0,
            service.basePrice,
            formData.selectedHours
          );
          setCurrentPricing(result);
        }
      } else {
        setCurrentPricing(null);
      }
    } else {
      setCurrentPricing(null);
    }
  }, [formData, discount]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous errors
    setErrors({});
    
    // Validate form
    const validation = validateQuoteForm(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }
    
    setIsSubmitting(true);

    try {
      const service = serviceConfig.find(s => s.id === formData.serviceType);
      if (!service) throw new Error('Service not found');

      // For hourly services, use 'small' as default property size since it's not used in calculation
      const isHourlyService = formData.serviceType && hourlyServices[formData.serviceType as keyof typeof hourlyServices];
      const propertySize = isHourlyService ? 'small' : formData.propertySize;

      const pricingData = pricingCalculator.calculate(
        formData.serviceType,
        propertySize as any,
        formData.frequency as any,
        formData.additionalServices,
        discount?.percentage || 0,
        service.basePrice,
        formData.selectedHours
      );

      // Prepare submission data with only relevant fields
      const baseData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        serviceType: formData.serviceType,
        propertySize: propertySize,
        frequency: formData.frequency,
        additionalServices: formData.additionalServices,
        message: formData.message,
        paymentMethod: formData.paymentMethod || 'fixed',
        pricing: pricingData,
        discountCode: discount ? 'Applied' : null,
        timestamp: new Date().toISOString(),
        status: 'pending'
      };

      // Add optional fields only if they have values
      const submissionData: QuoteSubmission = {
        ...baseData,
        ...(isHourlyService && formData.selectedHours && { selectedHours: formData.selectedHours }),
        ...(isHourlyService && isHourlyService.supplyMethod && { supplyMethod: isHourlyService.supplyMethod })
      };

      await firebaseService.addQuoteRequest(submissionData);
      
      // Track successful quote submission
      analyticsService.trackEvent('quote_request', 'form_submission', formData.serviceType, pricingData.total);
      
      setPricing(pricingData);
      setSubmitted(true);
      showSuccess('Quote request submitted successfully! We\'ll contact you within 24 hours.');
    } catch (error) {
      console.error('Error submitting form:', error);
      const errorMessage = error instanceof Error ? error.message : 'There was an error submitting your request. Please try again.';
      showError(errorMessage);
      setErrors({ submit: errorMessage });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      serviceType: '',
      propertySize: '',
      frequency: '',
      additionalServices: [],
      message: '',
      paymentMethod: 'fixed'
    });
    setPricing(null);
    setSubmitted(false);
    setErrors({});
    clearDiscount();
  };

  if (submitted) {
    return (
      <div className="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Thank You!</h2>
            <p className="text-gray-600 mb-6">
              Your request has been submitted. We'll contact you within 24 hours.
            </p>
            
            {pricing && (
              <div className="bg-green-50 p-4 rounded-lg mb-6">
                <h3 className="font-semibold text-gray-900 mb-2">Your Quote</h3>
                <div className="space-y-1 text-sm">
                  {pricing.isHourly ? (
                    <>
                      <div className="flex justify-between">
                        <span>Hourly Rate:</span>
                        <span>${pricing.hourlyRate}/hr</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Selected Hours:</span>
                        <span>{pricing.estimatedHours} hrs</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>${pricing.subtotal}</span>
                      </div>
                    </>
                  ) : (
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>${pricing.subtotal}</span>
                    </div>
                  )}
                  {pricing.discountAmount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Discount:</span>
                      <span>-${pricing.discountAmount}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-bold text-lg border-t pt-1">
                    <span>Total:</span>
                    <span className="text-green-600">${pricing.total}</span>
                  </div>
                </div>
              </div>
            )}
            
            <button
              onClick={resetForm}
              className="px-6 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors"
            >
              Get Another Quote
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <Calculator className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Get Your Free Quote</h1>
          <p className="text-gray-600">Fill out the form below and we'll provide you with an instant estimate</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid md:grid-cols-2 gap-4">
              <FormInput
                label="Full Name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                placeholder="John Doe"
                error={errors.name}
              />
              <FormInput
                label="Email Address"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                placeholder="<EMAIL>"
                error={errors.email}
              />
            </div>

            <FormInput
              label="Phone Number"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleInputChange}
              required
              placeholder="(*************"
              error={errors.phone}
            />

            <FormSelect
              label="Service Type"
              name="serviceType"
              value={formData.serviceType}
              onChange={handleInputChange}
              required
              error={errors.serviceType}
            >
              <option value="">Select a service</option>
              {serviceConfig.map((service) => (
                <option key={service.id} value={service.id}>
                  {service.name}
                </option>
              ))}
            </FormSelect>

            {formData.serviceType && hourlyServices[formData.serviceType as keyof typeof hourlyServices] && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Hourly Service Information</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Minimum booking: {hourlyServices[formData.serviceType as keyof typeof hourlyServices].minHours} hours</li>
                  <li>• Rate: ${hourlyServices[formData.serviceType as keyof typeof hourlyServices].rate}/hour</li>
                  {formData.serviceType === 'hourly-customer-supply' && (
                    <li>• You provide cleaning supplies and equipment</li>
                  )}
                  {formData.serviceType === 'hourly-company-supply' && (
                    <li>• We provide all professional-grade supplies and equipment</li>
                  )}
                  {formData.serviceType === 'backyard-hourly' && (
                    <li>• Outdoor cleaning and maintenance services</li>
                  )}
                </ul>
              </div>
            )}

            {formData.serviceType && hourlyServices[formData.serviceType as keyof typeof hourlyServices] && (
              <FormSelect
                label="Number of Hours"
                name="selectedHours"
                value={formData.selectedHours || ''}
                onChange={handleInputChange}
                required
                error={errors.selectedHours}
              >
                <option value="">Select hours</option>
                {Array.from({ length: 8 }, (_, i) => {
                  const hours = i + hourlyServices[formData.serviceType as keyof typeof hourlyServices].minHours;
                  const hourlyRate = hourlyServices[formData.serviceType as keyof typeof hourlyServices].rate;
                  const totalCost = hours * hourlyRate;
                  return (
                    <option key={hours} value={hours}>
                      {hours} hour{hours > 1 ? 's' : ''} (${totalCost} base cost)
                    </option>
                  );
                })}
              </FormSelect>
            )}

            {/* Property Size - only show for non-hourly services */}
            {formData.serviceType && !hourlyServices[formData.serviceType as keyof typeof hourlyServices] && (
              <FormSelect
                label="Property Size"
                name="propertySize"
                value={formData.propertySize}
                onChange={handleInputChange}
                required
                error={errors.propertySize}
              >
                <option value="">Select property size</option>
                <option value="small">Small (1-2 rooms/under 1000 sq ft)</option>
                <option value="medium">Medium (3-4 rooms/1000-2000 sq ft)</option>
                <option value="large">Large (5-6 rooms/3000 sq ft)</option>
                <option value="xlarge">Extra Large (7+ rooms/4000+ sq ft)</option>
              </FormSelect>
            )}

            <FormSelect
              label="Cleaning Frequency"
              name="frequency"
              value={formData.frequency}
              onChange={handleInputChange}
              required
              error={errors.frequency}
            >
              <option value="">Select frequency</option>
              <option value="one-time">One-time cleaning</option>
              <option value="weekly">Weekly</option>
              <option value="bi-weekly">Bi-weekly</option>
              <option value="monthly">Monthly</option>
            </FormSelect>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Services (optional)
              </label>
              <div className="grid md:grid-cols-2 gap-2">
                {[
                  'Deep cleaning',
                  'Carpet cleaning',
                  'Window cleaning',
                  'Appliance cleaning',
                  'Garage cleaning',
                  'Basement cleaning'
                ].map((service) => {
                  const isHourlyService = formData.serviceType && hourlyServices[formData.serviceType as keyof typeof hourlyServices];
                  const additionalServicePrice = isHourlyService ? 15 : 25;
                  
                  return (
                    <label key={service} className="flex items-center text-sm">
                      <input
                        type="checkbox"
                        name="additionalServices"
                        value={service}
                        checked={formData.additionalServices.includes(service)}
                        onChange={handleInputChange}
                        className="mr-2 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <span className="text-gray-700">
                        {service} (+${additionalServicePrice}{isHourlyService ? '/service' : ''})
                      </span>
                    </label>
                  );
                })}
              </div>
            </div>

            <FormTextarea
              label="Additional Notes (Optional)"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              rows={3}
              placeholder="Tell us about any specific requirements or preferences..."
            />

            {errors.submit && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{errors.submit}</p>
              </div>
            )}

            <Suspense fallback={<LoadingSpinner size="small" text="Loading discount options..." />}>
              <DiscountCodeInput
                onApply={applyDiscountCode}
                appliedDiscount={discount}
                isLoading={isLoading}
                onClear={clearDiscount}
                error={error}
              />
            </Suspense>

            {currentPricing && (
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-2">Instant Estimate</h3>
                <div className="space-y-1 text-sm">
                  {currentPricing.isHourly ? (
                    <>
                      <div className="flex justify-between">
                        <span>Hourly Rate:</span>
                        <span>${currentPricing.hourlyRate}/hr</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Selected Hours:</span>
                        <span>{currentPricing.estimatedHours} hrs</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>${currentPricing.subtotal}</span>
                      </div>
                    </>
                  ) : (
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>${currentPricing.subtotal}</span>
                    </div>
                  )}
                  {currentPricing.discountAmount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Discount ({discount?.percentage}%):</span>
                      <span>-${currentPricing.discountAmount}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-bold text-lg border-t pt-1">
                    <span>Total:</span>
                    <span className="text-green-600">${currentPricing.total}</span>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  {currentPricing.isHourly 
                    ? '*Final pricing will be confirmed after consultation'
                    : '*Final pricing will be confirmed after consultation'
                  }
                </p>
              </div>
            )}

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? 'Submitting...' : 'Get My Quote'}
            </button>
          </form>
        </div>

        {/* Toast notifications */}
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            message={toast.message}
            type={toast.type}
            duration={toast.duration}
            onClose={() => removeToast(toast.id)}
          />
        ))}
      </div>
    </div>
  );
};

export default QuotePage;
