// Google Analytics service for tracking page views and user activity
import { firebaseService } from './firebase';

export interface PageAnalytics {
  page: string;
  title: string;
  activeUsers: number;
  totalPageViews: number;
  timestamp: string;
}

export interface RealTimeAnalytics {
  totalActiveUsers: number;
  pageAnalytics: PageAnalytics[];
  lastUpdated: string;
}

class AnalyticsService {
  private realTimeData: RealTimeAnalytics = {
    totalActiveUsers: 0,
    pageAnalytics: [],
    lastUpdated: new Date().toISOString()
  };

  private updateInterval: number | null = null;
  private gaLoaded: boolean = false;
  private measurementId: string = import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || '';

  // Initialize Google Analytics tracking
  init() {
    this.loadGoogleAnalytics();
  }

  // Load Google Analytics script dynamically
  private loadGoogleAnalytics() {
    if (typeof window === 'undefined' || !this.measurementId || this.measurementId === 'VITE_FIREBASE_MEASUREMENT_ID' || this.gaLoaded) {
      return;
    }

    // Load Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${this.measurementId}`;
    document.head.appendChild(script);

    // Initialize Google Analytics
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      window.dataLayer.push(args);
    }
    
    // Make gtag globally available
    window.gtag = gtag;
    
    gtag('js', new Date());
    gtag('config', this.measurementId);
    
    this.gaLoaded = true;

    // Track current page
    this.trackPageView(window.location.pathname, document.title);
  }

  // Track page view
  trackPageView(page: string, title: string) {
    if (typeof window !== 'undefined' && typeof window.gtag === 'function' && this.measurementId) {
      window.gtag('config', this.measurementId, {
        page_path: page,
        page_title: title
      });

      // Also track in Firebase for real-time dashboard
      this.trackPageViewInFirebase(page, title);
    }
  }

  // Track page view in Firebase for real-time analytics
  private async trackPageViewInFirebase(page: string, title: string) {
    try {
      const sessionId = this.getSessionId();
      const timestamp = new Date().toISOString();
      
      // Store page view data
      await firebaseService.addDocument('page_analytics', {
        page,
        title,
        sessionId,
        timestamp,
        userAgent: navigator.userAgent,
        referrer: document.referrer || 'direct'
      });

      // Update active users count
      await this.updateActiveUsers(page);
    } catch (error) {
      console.error('Error tracking page view:', error);
    }
  }

  // Update active users for a specific page
  private async updateActiveUsers(page: string) {
    try {
      const sessionId = this.getSessionId();
      const timestamp = new Date().toISOString();
      
      // Update or create active user record
      await firebaseService.updateDocument('active_users', sessionId, {
        currentPage: page,
        lastActivity: timestamp,
        sessionId
      });
    } catch (error) {
      console.error('Error updating active users:', error);
    }
  }

  // Get or create session ID
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('analytics_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('analytics_session_id', sessionId);
    }
    return sessionId;
  }

  // Get real-time analytics data
  async getRealTimeAnalytics(): Promise<RealTimeAnalytics> {
    try {
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

      // Get active users (users active in last 5 minutes)
      const activeUsersQuery = await firebaseService.getDocuments('active_users', [
        {
          field: 'lastActivity',
          operator: '>=',
          value: fiveMinutesAgo.toISOString()
        }
      ]);

      // Get page analytics for active users
      const pageAnalyticsMap = new Map<string, PageAnalytics>();
      
      activeUsersQuery.forEach((user: any) => {
        const page = user.currentPage;
        if (pageAnalyticsMap.has(page)) {
          const existing = pageAnalyticsMap.get(page)!;
          pageAnalyticsMap.set(page, {
            ...existing,
            activeUsers: existing.activeUsers + 1
          });
        } else {
          pageAnalyticsMap.set(page, {
            page,
            title: this.getPageTitle(page),
            activeUsers: 1,
            totalPageViews: 0,
            timestamp: now.toISOString()
          });
        }
      });

      // Get total page views for today
      const startOfDay = new Date();
      startOfDay.setHours(0, 0, 0, 0);
      
      const pageViewsQuery = await firebaseService.getDocuments('page_analytics', [
        {
          field: 'timestamp',
          operator: '>=',
          value: startOfDay.toISOString()
        }
      ]);

      // Count page views per page
      const pageViewCounts = new Map<string, number>();
      pageViewsQuery.forEach((view: any) => {
        const page = view.page;
        pageViewCounts.set(page, (pageViewCounts.get(page) || 0) + 1);
      });

      // Update page analytics with total views
      Array.from(pageAnalyticsMap.keys()).forEach(page => {
        const analytics = pageAnalyticsMap.get(page)!;
        analytics.totalPageViews = pageViewCounts.get(page) || 0;
        pageAnalyticsMap.set(page, analytics);
      });

      // Add pages with views but no active users
      pageViewCounts.forEach((count, page) => {
        if (!pageAnalyticsMap.has(page)) {
          pageAnalyticsMap.set(page, {
            page,
            title: this.getPageTitle(page),
            activeUsers: 0,
            totalPageViews: count,
            timestamp: now.toISOString()
          });
        }
      });

      this.realTimeData = {
        totalActiveUsers: activeUsersQuery.length,
        pageAnalytics: Array.from(pageAnalyticsMap.values()).sort((a, b) => b.activeUsers - a.activeUsers),
        lastUpdated: now.toISOString()
      };

      return this.realTimeData;
    } catch (error) {
      console.error('Error getting real-time analytics:', error);
      return this.realTimeData;
    }
  }

  // Get page title from path
  private getPageTitle(path: string): string {
    const titles: { [key: string]: string } = {
      '/': 'Home',
      '/services': 'Services',
      '/quote': 'Get Quote',
      '/contact': 'Contact Us',
      '/admin': 'Admin Dashboard',
      '/login': 'Login'
    };
    return titles[path] || path;
  }

  // Start real-time tracking for admin dashboard
  startRealTimeTracking(callback: (data: RealTimeAnalytics) => void) {
    // Update every 10 seconds
    this.updateInterval = window.setInterval(async () => {
      const data = await this.getRealTimeAnalytics();
      callback(data);
    }, 10000);

    // Initial load
    this.getRealTimeAnalytics().then(callback);
  }

  // Stop real-time tracking
  stopRealTimeTracking() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  // Track custom events
  trackEvent(action: string, category: string, label?: string, value?: number) {
    if (typeof window !== 'undefined' && typeof window.gtag === 'function') {
      window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value
      });
    }
  }

  // Clean up old data (call this periodically)
  async cleanupOldData() {
    try {
      const oneDayAgo = new Date();
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);

      // Remove old active users
      const oldActiveUsers = await firebaseService.getDocuments('active_users', [
        {
          field: 'lastActivity',
          operator: '<',
          value: oneDayAgo.toISOString()
        }
      ]);

      for (const user of oldActiveUsers) {
        await firebaseService.deleteDocument('active_users', user.id);
      }

      // Keep page analytics for longer (30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const oldPageAnalytics = await firebaseService.getDocuments('page_analytics', [
        {
          field: 'timestamp',
          operator: '<',
          value: thirtyDaysAgo.toISOString()
        }
      ]);

      for (const analytics of oldPageAnalytics) {
        await firebaseService.deleteDocument('page_analytics', analytics.id);
      }
    } catch (error) {
      console.error('Error cleaning up old analytics data:', error);
    }
  }
}

// Extend Window interface for gtag
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js',
      targetId: string | Date,
      config?: any
    ) => void;
    dataLayer: any[];
  }
}

export const analyticsService = new AnalyticsService();
