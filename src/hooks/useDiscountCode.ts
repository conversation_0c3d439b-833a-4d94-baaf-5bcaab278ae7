import { useState } from 'react';
import { firebaseService } from '../services/firebase';
import type { DiscountCode } from '../types';

export const useDiscountCode = () => {
  const [discountCode, setDiscountCode] = useState('');
  const [discount, setDiscount] = useState<DiscountCode | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const applyDiscountCode = async (code: string) => {
    if (!code.trim()) {
      setError('Please enter a discount code');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Normalize the code to uppercase
      const normalizedCode = code.trim().toUpperCase();
      const discountData = await firebaseService.getDiscountCode(normalizedCode);
      
      if (!discountData) {
        setError('Invalid discount code');
        setDiscount(null);
      } else if (!discountData.active) {
        setError('This discount code has expired');
        setDiscount(null);
      } else {
        setDiscount(discountData);
        setDiscountCode(normalizedCode);
        setError(null);
      }
    } catch {
      setError('Failed to validate discount code');
      setDiscount(null);
    } finally {
      setIsLoading(false);
    }
  };

  const clearDiscount = () => {
    setDiscount(null);
    setDiscountCode('');
    setError(null);
  };

  return {
    discountCode,
    discount,
    isLoading,
    error,
    applyDiscountCode,
    clearDiscount,
    setDiscountCode
  };
};
