import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { analyticsService } from '../services/analytics';

// Hook to track page views automatically
export const usePageTracking = () => {
  const location = useLocation();

  useEffect(() => {
    // Track page view when location changes
    const path = location.pathname;
    const title = document.title;
    
    analyticsService.trackPageView(path, title);
  }, [location]);
};

// Hook for real-time analytics in admin dashboard
export const useRealTimeAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    
    const handleAnalyticsUpdate = (data: any) => {
      setAnalyticsData(data);
      setIsLoading(false);
    };

    // Start real-time tracking
    analyticsService.startRealTimeTracking(handleAnalyticsUpdate);

    // Cleanup on unmount
    return () => {
      analyticsService.stopRealTimeTracking();
    };
  }, []);

  return { analyticsData, isLoading };
};
