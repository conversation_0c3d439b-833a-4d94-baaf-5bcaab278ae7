import React, { lazy, Suspense } from 'react';
import type { ComponentType } from 'react';
import LoadingSpinner from './LoadingSpinner';
import ErrorBoundary from '../components/ErrorBoundary';

interface LazyWrapperProps {
  fallback?: React.ReactNode;
  error?: React.ReactNode;
}

export const withLazyLoading = <P extends object>(
  importFunc: () => Promise<{ default: ComponentType<P> }>,
  options: LazyWrapperProps = {}
) => {
  const LazyComponent = lazy(importFunc);
  
  const WrappedComponent: React.FC<P> = (props) => {
    const fallback = options.fallback || <LoadingSpinner size="large" />;
    
    return (
      <ErrorBoundary fallback={options.error}>
        <Suspense fallback={fallback}>
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  };
  
  return WrappedComponent;
};
