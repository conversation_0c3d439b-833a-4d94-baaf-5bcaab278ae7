import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import FormInput from '../FormInput';
import FormSelect from '../FormSelect';
import FormTextarea from '../FormTextarea';

describe('Form Components', () => {
  describe('FormInput Component', () => {
    const mockOnChange = vi.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
    });

    it('should render input with label', () => {
      render(
        <FormInput
          label="Test Label"
          name="test"
          value=""
          onChange={mockOnChange}
        />
      );

      expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should render required indicator', () => {
      render(
        <FormInput
          label="Test Label"
          name="test"
          value=""
          onChange={mockOnChange}
          required
        />
      );

      expect(screen.getByText('*')).toBeInTheDocument();
    });

    it('should display error message', () => {
      render(
        <FormInput
          label="Test Label"
          name="test"
          value=""
          onChange={mockOnChange}
          error="This field is required"
        />
      );

      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });

    it('should handle input changes', async () => {
      const user = userEvent.setup();
      
      render(
        <FormInput
          label="Test Label"
          name="test"
          value=""
          onChange={mockOnChange}
        />
      );

      const input = screen.getByRole('textbox');
      await user.type(input, 'test value');

      expect(mockOnChange).toHaveBeenCalled();
    });

    it('should apply error styling when error is present', () => {
      render(
        <FormInput
          label="Test Label"
          name="test"
          value=""
          onChange={mockOnChange}
          error="Error message"
        />
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('border-red-300');
    });

    it('should handle different input types', () => {
      render(
        <FormInput
          label="Email"
          name="email"
          type="email"
          value=""
          onChange={mockOnChange}
        />
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('type', 'email');
    });

    it('should handle placeholder text', () => {
      render(
        <FormInput
          label="Test Label"
          name="test"
          value=""
          onChange={mockOnChange}
          placeholder="Enter value"
        />
      );

      expect(screen.getByPlaceholderText('Enter value')).toBeInTheDocument();
    });

    it('should handle disabled state', () => {
      render(
        <FormInput
          label="Test Label"
          name="test"
          value=""
          onChange={mockOnChange}
          disabled
        />
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });
  });

  describe('FormSelect Component', () => {
    const mockOnChange = vi.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
    });

    it('should render select with label', () => {
      render(
        <FormSelect
          label="Test Select"
          name="test"
          value=""
          onChange={mockOnChange}
        >
          <option value="">Select option</option>
          <option value="option1">Option 1</option>
          <option value="option2">Option 2</option>
        </FormSelect>
      );

      expect(screen.getByLabelText('Test Select')).toBeInTheDocument();
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('should render all options', () => {
      render(
        <FormSelect
          label="Test Select"
          name="test"
          value=""
          onChange={mockOnChange}
        >
          <option value="">Select option</option>
          <option value="option1">Option 1</option>
          <option value="option2">Option 2</option>
        </FormSelect>
      );

      expect(screen.getByText('Select option')).toBeInTheDocument();
      expect(screen.getByText('Option 1')).toBeInTheDocument();
      expect(screen.getByText('Option 2')).toBeInTheDocument();
    });

    it('should handle selection changes', async () => {
      const user = userEvent.setup();
      
      render(
        <FormSelect
          label="Test Select"
          name="test"
          value=""
          onChange={mockOnChange}
        >
          <option value="">Select option</option>
          <option value="option1">Option 1</option>
          <option value="option2">Option 2</option>
        </FormSelect>
      );

      const select = screen.getByRole('combobox');
      await user.selectOptions(select, 'option1');

      expect(mockOnChange).toHaveBeenCalled();
    });

    it('should display error message', () => {
      render(
        <FormSelect
          label="Test Select"
          name="test"
          value=""
          onChange={mockOnChange}
          error="Please select an option"
        >
          <option value="">Select option</option>
        </FormSelect>
      );

      expect(screen.getByText('Please select an option')).toBeInTheDocument();
    });

    it('should apply error styling', () => {
      render(
        <FormSelect
          label="Test Select"
          name="test"
          value=""
          onChange={mockOnChange}
          error="Error message"
        >
          <option value="">Select option</option>
        </FormSelect>
      );

      const select = screen.getByRole('combobox');
      expect(select).toHaveClass('border-red-300');
    });

    it('should show required indicator', () => {
      render(
        <FormSelect
          label="Test Select"
          name="test"
          value=""
          onChange={mockOnChange}
          required
        >
          <option value="">Select option</option>
        </FormSelect>
      );

      expect(screen.getByText('*')).toBeInTheDocument();
    });
  });

  describe('FormTextarea Component', () => {
    const mockOnChange = vi.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
    });

    it('should render textarea with label', () => {
      render(
        <FormTextarea
          label="Test Textarea"
          name="test"
          value=""
          onChange={mockOnChange}
        />
      );

      expect(screen.getByLabelText('Test Textarea')).toBeInTheDocument();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle text input', async () => {
      const user = userEvent.setup();
      
      render(
        <FormTextarea
          label="Test Textarea"
          name="test"
          value=""
          onChange={mockOnChange}
        />
      );

      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'This is a test message');

      expect(mockOnChange).toHaveBeenCalled();
    });

    it('should handle rows prop', () => {
      render(
        <FormTextarea
          label="Test Textarea"
          name="test"
          value=""
          onChange={mockOnChange}
          rows={5}
        />
      );

      const textarea = screen.getByRole('textbox');
      expect(textarea).toHaveAttribute('rows', '5');
    });

    it('should handle placeholder', () => {
      render(
        <FormTextarea
          label="Test Textarea"
          name="test"
          value=""
          onChange={mockOnChange}
          placeholder="Enter your message"
        />
      );

      expect(screen.getByPlaceholderText('Enter your message')).toBeInTheDocument();
    });

    it('should display error message', () => {
      render(
        <FormTextarea
          label="Test Textarea"
          name="test"
          value=""
          onChange={mockOnChange}
          error="Message is required"
        />
      );

      expect(screen.getByText('Message is required')).toBeInTheDocument();
    });

    it('should apply error styling', () => {
      render(
        <FormTextarea
          label="Test Textarea"
          name="test"
          value=""
          onChange={mockOnChange}
          error="Error message"
        />
      );

      const textarea = screen.getByRole('textbox');
      expect(textarea).toHaveClass('border-red-300');
    });

    it('should show required indicator', () => {
      render(
        <FormTextarea
          label="Test Textarea"
          name="test"
          value=""
          onChange={mockOnChange}
          required
        />
      );

      expect(screen.getByText('*')).toBeInTheDocument();
    });

    it('should handle disabled state', () => {
      render(
        <FormTextarea
          label="Test Textarea"
          name="test"
          value=""
          onChange={mockOnChange}
          disabled
        />
      );

      const textarea = screen.getByRole('textbox');
      expect(textarea).toBeDisabled();
    });
  });

  describe('Form Component Integration', () => {
    it('should work together in a form', async () => {
      const user = userEvent.setup();
      const mockSubmit = vi.fn();
      
      const TestForm = () => {
        const [formData, setFormData] = React.useState({
          name: '',
          email: '',
          service: '',
          message: ''
        });

        const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
          setFormData(prev => ({
            ...prev,
            [e.target.name]: e.target.value
          }));
        };

        const handleSubmit = (e: React.FormEvent) => {
          e.preventDefault();
          mockSubmit(formData);
        };

        return (
          <form onSubmit={handleSubmit}>
            <FormInput
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
            />
            <FormInput
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
            />
            <FormSelect
              label="Service"
              name="service"
              value={formData.service}
              onChange={handleChange}
            >
              <option value="">Select service</option>
              <option value="cleaning">Cleaning</option>
            </FormSelect>
            <FormTextarea
              label="Message"
              name="message"
              value={formData.message}
              onChange={handleChange}
            />
            <button type="submit">Submit</button>
          </form>
        );
      };

      render(<TestForm />);

      // Fill out the form
      await user.type(screen.getByLabelText('Name'), 'John Doe');
      await user.type(screen.getByLabelText('Email'), '<EMAIL>');
      await user.selectOptions(screen.getByLabelText('Service'), 'cleaning');
      await user.type(screen.getByLabelText('Message'), 'Test message');

      // Submit the form
      await user.click(screen.getByText('Submit'));

      expect(mockSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
        service: 'cleaning',
        message: 'Test message'
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper labels and associations', () => {
      render(
        <FormInput
          label="Test Input"
          name="test"
          value=""
          onChange={() => {}}
        />
      );

      const input = screen.getByRole('textbox');
      const label = screen.getByText('Test Input');
      
      expect(input).toHaveAttribute('id', 'test');
      expect(label).toHaveAttribute('for', 'test');
    });

    it('should support aria-describedby for errors', () => {
      render(
        <FormInput
          label="Test Input"
          name="test"
          value=""
          onChange={() => {}}
          error="Error message"
        />
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-describedby', 'test-error');
    });

    it('should indicate required fields to screen readers', () => {
      render(
        <FormInput
          label="Test Input"
          name="test"
          value=""
          onChange={() => {}}
          required
        />
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-required', 'true');
    });
  });
});
