import React, { memo } from 'react';
import { Loader } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'medium', 
  text = 'Loading...' 
}) => {
  const sizeClasses = {
    small: 'h-4 w-4',
    medium: 'h-8 w-8',
    large: 'h-12 w-12'
  };

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <Loader className={`${sizeClasses[size]} text-green-600 animate-spin mb-2`} />
      <p className="text-gray-600 text-sm">{text}</p>
    </div>
  );
};

LoadingSpinner.displayName = 'LoadingSpinner';

export default memo(LoadingSpinner);
