import React, { memo } from 'react';

interface FormSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label: string;
  required?: boolean;
  error?: string;
  children: React.ReactNode;
}

const FormSelect: React.FC<FormSelectProps> = memo(({ 
  label, 
  required, 
  error, 
  children,
  id,
  name,
  ...props 
}) => {
  const selectId = id || name || '';
  const errorId = error ? `${selectId}-error` : undefined;

  return (
    <div>
      <label htmlFor={selectId} className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <select
        {...props}
        id={selectId}
        name={name}
        aria-required={required}
        aria-describedby={errorId}
        className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
          error ? 'border-red-300' : 'border-gray-300'
        }`}
      >
        {children}
      </select>
      {error && (
        <p id={errorId} data-testid={errorId} className="mt-1 text-xs text-red-600">
          {error}
        </p>
      )}
    </div>
  );
});

FormSelect.displayName = 'FormSelect';

export default FormSelect;
