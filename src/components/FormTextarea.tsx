import React, { memo } from 'react';

interface FormTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  required?: boolean;
  error?: string;
}

const FormTextarea: React.FC<FormTextareaProps> = memo(({ 
  label, 
  required, 
  error, 
  ...props 
}) => {
  const textareaId = props.id || props.name || 'textarea';
  const errorId = error ? `${textareaId}-error` : undefined;

  return (
    <div>
      <label 
        htmlFor={textareaId}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <textarea
        {...props}
        id={textareaId}
        aria-describedby={errorId}
        aria-invalid={!!error}
        className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
          error ? 'border-red-300' : 'border-gray-300'
        }`}
      />
      {error && (
        <p 
          id={errorId}
          data-testid={`${textareaId}-error`}
          className="mt-1 text-xs text-red-600"
        >
          {error}
        </p>
      )}
    </div>
  );
});

FormTextarea.displayName = 'FormTextarea';

export default FormTextarea;
