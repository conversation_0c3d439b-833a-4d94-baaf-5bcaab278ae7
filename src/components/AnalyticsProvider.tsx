import { useEffect } from 'react';
import { usePageTracking } from '../hooks/useAnalytics';
import { analyticsService } from '../services/analytics';

// Component to handle analytics initialization and page tracking
const AnalyticsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use the page tracking hook
  usePageTracking();

  useEffect(() => {
    // Initialize analytics service
    analyticsService.init();

    // Clean up old data periodically (every hour)
    const cleanupInterval = setInterval(() => {
      analyticsService.cleanupOldData();
    }, 60 * 60 * 1000); // 1 hour

    return () => {
      clearInterval(cleanupInterval);
    };
  }, []);

  return <>{children}</>;
};

export default AnalyticsProvider;
