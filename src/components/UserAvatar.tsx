import React, { useState } from 'react';

interface UserAvatarProps {
  user?: {
    photoURL?: string | null;
    displayName?: string | null;
    email?: string | null;
  } | null;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const UserAvatar: React.FC<UserAvatarProps> = ({ user, size = 'md', className = '' }) => {
  const [imageError, setImageError] = useState(false);
  
  const sizeClasses = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-sm',
    lg: 'h-12 w-12 text-lg'
  };
  
  const getInitials = () => {
    if (user?.displayName) {
      return user.displayName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    if (user?.email) {
      return user.email[0].toUpperCase();
    }
    return 'A';
  };
  
  const avatarClasses = `${sizeClasses[size]} rounded-full border-2 border-white shadow-sm overflow-hidden bg-green-600 flex items-center justify-center ${className}`;
  
  if (!user?.photoURL || imageError) {
    return (
      <div className={avatarClasses}>
        <span className="text-white font-medium">
          {getInitials()}
        </span>
      </div>
    );
  }
  
  return (
    <div className={avatarClasses}>
      <img 
        src={user.photoURL} 
        alt={user?.displayName || 'User'}
        className="h-full w-full object-cover"
        onError={() => setImageError(true)}
        onLoad={() => setImageError(false)}
      />
    </div>
  );
};

export default UserAvatar;
