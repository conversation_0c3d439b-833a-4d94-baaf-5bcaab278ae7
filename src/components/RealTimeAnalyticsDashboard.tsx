import React from 'react';
import { Users, Eye, Clock, TrendingUp } from 'lucide-react';
import { useRealTimeAnalytics } from '../hooks/useAnalytics';
import LoadingSpinner from './LoadingSpinner';

interface PageAnalyticsCardProps {
  page: string;
  title: string;
  activeUsers: number;
  totalPageViews: number;
}

const PageAnalyticsCard: React.FC<PageAnalyticsCardProps> = ({
  title,
  activeUsers,
  totalPageViews
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">{title}</h3>
        <div className="flex items-center space-x-2">
          <div className="flex items-center text-green-600">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
            <span className="text-xs font-medium">{activeUsers} live</span>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="flex items-center text-gray-500 mb-1">
            <Users className="w-4 h-4 mr-1" />
            <span className="text-xs">Active Now</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">{activeUsers}</p>
        </div>
        
        <div>
          <div className="flex items-center text-gray-500 mb-1">
            <Eye className="w-4 h-4 mr-1" />
            <span className="text-xs">Views Today</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">{totalPageViews}</p>
        </div>
      </div>
    </div>
  );
};

const RealTimeAnalyticsDashboard: React.FC = () => {
  const { analyticsData, isLoading } = useRealTimeAnalytics();

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <LoadingSpinner size="medium" text="Loading real-time analytics..." />
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center text-gray-500">
          <Users className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>No analytics data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Active Users</p>
              <p className="text-3xl font-bold text-gray-900">{analyticsData.totalActiveUsers}</p>
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            <span className="text-sm text-gray-500">Live tracking</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Page Views</p>
              <p className="text-3xl font-bold text-gray-900">
                {analyticsData.pageAnalytics.reduce((sum: number, page: any) => sum + page.totalPageViews, 0)}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">Today</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Eye className="w-8 h-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Pages</p>
              <p className="text-3xl font-bold text-gray-900">
                {analyticsData.pageAnalytics.filter((page: any) => page.activeUsers > 0).length}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">With live users</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="w-8 h-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Last Updated</p>
              <p className="text-lg font-bold text-gray-900">
                {new Date(analyticsData.lastUpdated).toLocaleTimeString()}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">Auto-refresh: 10s</span>
          </div>
        </div>
      </div>

      {/* Page Analytics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Page Analytics</h2>
          <p className="text-sm text-gray-500 mt-1">Real-time user activity by page</p>
        </div>
        
        <div className="p-6">
          {analyticsData.pageAnalytics.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">No page activity detected</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {analyticsData.pageAnalytics.map((page: any, index: number) => (
                <PageAnalyticsCard
                  key={`${page.page}-${index}`}
                  page={page.page}
                  title={page.title}
                  activeUsers={page.activeUsers}
                  totalPageViews={page.totalPageViews}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Most Popular Pages */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Most Popular Pages Today</h2>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {analyticsData.pageAnalytics
              .sort((a: any, b: any) => b.totalPageViews - a.totalPageViews)
              .slice(0, 5)
              .map((page: any, index: number) => (
                <div key={`popular-${page.page}-${index}`} className="flex items-center justify-between py-2">
                  <div className="flex items-center">
                    <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 text-sm font-medium mr-3">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{page.title}</p>
                      <p className="text-sm text-gray-500">{page.page}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{page.totalPageViews} views</p>
                    <p className="text-sm text-gray-500">{page.activeUsers} active</p>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeAnalyticsDashboard;
