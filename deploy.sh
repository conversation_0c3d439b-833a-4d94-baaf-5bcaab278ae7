#!/bin/bash

# Ottawa Shine Solutions - Automated Deployment Script
# This script builds and deploys the cleaning company website

set -e  # Exit on any error

echo "🧹 Ottawa Shine Solutions - Deployment Script"
echo "=============================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    echo "Please copy .env.example to .env and configure your Firebase settings."
    exit 1
fi

print_status ".env file found"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    print_error "Firebase CLI is not installed."
    echo "Install it with: npm install -g firebase-tools"
    exit 1
fi

print_status "Firebase CLI is ready"

# Check if user is logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    print_error "Please log in to Firebase first:"
    echo "Run: firebase login"
    exit 1
fi

print_status "Firebase authentication verified"

# Get current Firebase project
PROJECT=$(firebase use --json 2>/dev/null | jq -r '.result.current // "No project selected"' 2>/dev/null || echo "No project selected")

if [ "$PROJECT" = "No project selected" ] || [ "$PROJECT" = "null" ]; then
    print_error "No Firebase project selected."
    echo "Run: firebase use <project-id>"
    exit 1
fi

print_status "Firebase project: $PROJECT"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
if npm install; then
    print_status "Dependencies installed"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Run linting
echo ""
echo "🔍 Running linter..."
if npm run lint; then
    print_status "Linting passed"
else
    print_warning "Linting issues found (continuing anyway)"
fi

# Build the application
echo ""
echo "🏗️  Building application..."
if npm run build; then
    print_status "Build successful"
else
    print_error "Build failed"
    exit 1
fi

# Check if dist folder exists
if [ ! -d "dist" ]; then
    print_error "Build output (dist folder) not found"
    exit 1
fi

print_status "Build output verified"

# Ask user what to deploy
echo ""
echo "🚀 What would you like to deploy?"
echo "1) Everything (hosting + firestore rules)"
echo "2) Hosting only"
echo "3) Firestore rules only"
echo "4) Cancel"

read -p "Choose an option (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🚀 Deploying everything..."
        if firebase deploy; then
            print_status "Full deployment successful!"
        else
            print_error "Deployment failed"
            exit 1
        fi
        ;;
    2)
        echo ""
        echo "🌍 Deploying hosting only..."
        if firebase deploy --only hosting; then
            print_status "Hosting deployment successful!"
        else
            print_error "Hosting deployment failed"
            exit 1
        fi
        ;;
    3)
        echo ""
        echo "🔒 Deploying Firestore rules only..."
        if firebase deploy --only firestore:rules; then
            print_status "Firestore rules deployment successful!"
        else
            print_error "Firestore rules deployment failed"
            exit 1
        fi
        ;;
    4)
        echo "Deployment cancelled."
        exit 0
        ;;
    *)
        print_error "Invalid option selected"
        exit 1
        ;;
esac

# Get hosting URL
HOSTING_URL=$(firebase hosting:channel:list --json 2>/dev/null | jq -r '.result[] | select(.name == "live") | .url' 2>/dev/null || echo "")

if [ -z "$HOSTING_URL" ]; then
    HOSTING_URL="https://$PROJECT.web.app"
fi

echo ""
echo "🎉 Deployment Complete!"
echo "========================"
echo ""
echo "🌍 Website URL: $HOSTING_URL"
echo "🔥 Firebase Console: https://console.firebase.google.com/project/$PROJECT"
echo ""
echo "📋 Post-Deployment Checklist:"
echo "- Test website functionality"
echo "- Verify quote form submission"
echo "- Test contact form"
echo "- Check discount code functionality"
echo "- Test admin login and dashboard"
echo ""
echo "📊 Monitor your deployment:"
echo "- Hosting usage: Firebase Console > Hosting"
echo "- Firestore usage: Firebase Console > Firestore"
echo "- Error logs: Firebase Console > Functions (if any)"
echo ""
print_status "Ottawa Shine Solutions is live! 🧹✨"
