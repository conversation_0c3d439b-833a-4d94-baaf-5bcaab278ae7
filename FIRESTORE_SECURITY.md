# Firestore Security Rules Documentation

## Overview
This document explains the Firestore security rules implemented for Ottawa Shine Solutions cleaning company website. The rules are designed to provide proper access control for different user types while maintaining data security.

## User Roles and Permissions

### 1. Admin Users
**Identification**: Users with specific email addresses defined in the `isAdmin()` function
- `<EMAIL>`
- `<EMAIL>`
- Additional admin emails can be added to the array

**Permissions**:
- ✅ **Full read access** to all collections (quotes, contacts, discountCodes, admin)
- ✅ **Full write access** to all collections
- ✅ Can manage discount codes (create, update, delete)
- ✅ Can update quote and contact statuses
- ✅ Can delete quotes and contacts
- ✅ Access to analytics and admin-specific data

### 2. Regular Users (Website Visitors)
**Identification**: Any user (authenticated or unauthenticated)

**Permissions**:
- ✅ **Create quotes** (submit quote requests) - No login required
- ✅ **Create contact messages** (submit contact forms) - No login required
- ✅ **Read discount codes** - No login required to apply discounts
- ❌ **No read access** to quotes or contacts submitted by others
- ❌ **No admin functions**

## Collection-Specific Rules

### Quotes Collection (`/quotes/{quoteId}`)
```typescript
// Data Structure Validation
- name: string (required, non-empty)
- email: string (required, valid email format)
- phone: string (required, non-empty)
- serviceType: string (required, non-empty)
- propertySize: 'small' | 'medium' | 'large' | 'xlarge' (required)
- frequency: 'one-time' | 'weekly' | 'bi-weekly' | 'monthly' (required)
- message: string (required)
- timestamp: serverTimestamp (auto-generated)
- status: 'pending' (auto-set on creation)
```

**Access Rules**:
- **Create**: Anyone can submit a quote with valid data
- **Read/Update/Delete**: Admin only

### Contacts Collection (`/contacts/{contactId}`)
```typescript
// Data Structure Validation
- name: string (required, non-empty)
- email: string (required, valid email format)
- phone: string (required, non-empty)
- subject: string (required, non-empty)
- message: string (required, non-empty)
- timestamp: serverTimestamp (auto-generated)
- status: 'new' (auto-set on creation)
```

**Access Rules**:
- **Create**: Anyone can submit a contact message with valid data
- **Read/Update/Delete**: Admin only

### Discount Codes Collection (`/discountCodes/{codeId}`)
```typescript
// Data Structure
- percentage: number (discount percentage)
- active: boolean (whether code is active)
- createdAt: serverTimestamp
- updatedAt: serverTimestamp
```

**Access Rules**:
- **Read**: Anyone (no authentication required) - to apply discount codes
- **Write**: Admin only (create, update, delete codes)

### Page Analytics Collection (`/page_analytics/{analyticsId}`)
```typescript
// Data Structure
- page: string (page path, e.g., '/', '/services')
- title: string (page title)
- sessionId: string (unique session identifier)
- timestamp: serverTimestamp
- userAgent: string (browser information)
- referrer: string (referring page or 'direct')
```

**Access Rules**:
- **Create**: Anyone (no authentication required) - for tracking page views
- **Read**: Admin only - for analytics dashboard
- **Delete**: Anyone (system cleanup of old data)

### Active Users Collection (`/active_users/{sessionId}`)
```typescript
// Data Structure
- currentPage: string (current page path)
- lastActivity: serverTimestamp
- sessionId: string (unique session identifier)
```

**Access Rules**:
- **Create/Update**: Anyone (no authentication required) - for real-time tracking
- **Read**: Admin only - for live analytics dashboard
- **Delete**: Anyone (system cleanup of old sessions)

### Admin Collection (`/admin/{document}`)
**Access Rules**:
- **Read/Write**: Admin only
- Used for analytics, settings, and admin-specific data

### Users Collection (`/users/{userId}`)
**Access Rules**:
- **Read/Write**: Users can access their own profile only
- **Read**: Admin can read all user profiles

## Security Features

### 1. Data Validation
- **Email Format**: Validates proper email format using regex
- **Required Fields**: Ensures all required fields are present
- **Value Constraints**: Validates enum values for propertySize and frequency
- **String Length**: Ensures non-empty strings for required fields

### 2. Authentication Checks
- **Admin Verification**: Email-based admin identification
- **User Authentication**: Checks for valid authentication tokens
- **Timestamp Security**: Uses server timestamps to prevent client manipulation

### 3. Access Control
- **Principle of Least Privilege**: Users only get necessary permissions
- **Collection Isolation**: Users cannot access each other's data
- **Admin Separation**: Clear separation between user and admin functions

## Implementation Best Practices

### 1. Client-Side Code
```typescript
// Always handle errors when submitting data
try {
  await firebaseService.addQuoteRequest(quoteData);
} catch (error) {
  // Handle validation or permission errors
}

// Check admin status before showing admin features
const isAdmin = firebaseService.auth.isAdmin(user);
```

### 2. Error Handling
- **Validation Errors**: Clear error messages for invalid data
- **Permission Errors**: Appropriate error handling for unauthorized access
- **Network Errors**: Graceful handling of connection issues

### 3. Data Sanitization
- **Input Validation**: Client-side validation before submission
- **Server Validation**: Rules enforce server-side validation
- **XSS Prevention**: Proper data sanitization in client code

## Deployment Notes

### 1. Firebase Setup
1. Deploy rules using Firebase CLI: `firebase deploy --only firestore:rules`
2. Test rules in Firebase Console using the Rules Playground
3. Monitor Firebase Console for rule violations

### 2. Admin Management
1. Update admin email list in both `firestore.rules` and `firebase.ts`
2. Test admin functions after adding new admin users
3. Consider implementing role-based permissions for complex scenarios

### 3. Security Monitoring
1. Enable Firestore security alerts in Firebase Console
2. Monitor for unusual access patterns
3. Regular audit of admin user list
4. Review and update rules as application evolves

## Testing the Rules

### 1. Quote Submission Test
```javascript
// Should succeed - valid quote data
const validQuote = {
  name: "John Doe",
  email: "<EMAIL>",
  phone: "************",
  serviceType: "residential",
  propertySize: "medium",
  frequency: "weekly",
  message: "Need regular cleaning"
};

// Should fail - invalid email
const invalidQuote = {
  ...validQuote,
  email: "invalid-email"
};
```

### 2. Admin Access Test
- Test with admin email: Should have full access
- Test with regular user: Should only create quotes/contacts
- Test unauthenticated: Should only create quotes/contacts

### 3. Discount Code Test
- Any user (authenticated or not): Should read discount codes
- Admin: Should manage (CRUD) discount codes

## Maintenance

1. **Regular Reviews**: Quarterly review of security rules
2. **Update Dependencies**: Keep Firebase SDK updated
3. **Monitor Usage**: Track rule violations and adjust accordingly
4. **Documentation**: Keep this documentation updated with rule changes
