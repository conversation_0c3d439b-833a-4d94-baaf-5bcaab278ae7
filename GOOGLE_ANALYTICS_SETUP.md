# Google Analytics Setup Guide

## Setting Up Google Analytics for Ottawa Shine Solutions

### Step 1: Create a Google Analytics Account
1. Go to [Google Analytics](https://analytics.google.com/)
2. Sign in with your Google account
3. Click "Start measuring"
4. Set up your account:
   - Account name: "Ottawa Shine Solutions"
   - Data sharing settings: Choose your preferences

### Step 2: Create a Property
1. Property name: "Ottawa Shine Solutions Website"
2. Reporting time zone: "Canada/Eastern"
3. Currency: "Canadian Dollar (CAD)"

### Step 3: Set up Data Stream
1. Choose "Web" platform
2. Website URL: Your website URL (e.g., https://ottawashinesolutions.com)
3. Stream name: "Ottawa Shine Solutions Main Site"
4. Enhanced measurement: Enable all options

### Step 4: Get Your Measurement ID
1. After creating the data stream, you'll see a "Measurement ID" that looks like `G-XXXXXXXXXX`
2. Copy this ID

### Step 5: Update Your Configuration
The tracking code is already implemented in the application. You just need to:

1. **Update .env file**: Replace the `VITE_FIREBASE_MEASUREMENT_ID` value in your `.env` file with your actual Measurement ID
   ```
   VITE_FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX
   ```
   (Replace `G-XXXXXXXXXX` with your actual measurement ID)

2. **Restart your development server**: After updating the .env file, restart your dev server to pick up the new environment variable

### Step 6: Test Your Setup
1. Deploy your website
2. Visit your website
3. Go to Google Analytics > Reports > Realtime
4. You should see your visit appear within a few minutes

## Features Implemented

### Real-Time Analytics Dashboard
The admin panel now includes a "Live Analytics" tab that shows:

- **Total Active Users**: Number of users currently on your website
- **Page Analytics**: Real-time user activity by page
- **Total Page Views**: Daily page view counts
- **Most Popular Pages**: Rankings of your most visited pages

### Automatic Page Tracking
- Automatically tracks page views when users navigate
- Records page titles and paths
- Stores data in Firebase for the admin dashboard

### Data Storage
- Page views are stored in Firebase Firestore
- Active user sessions are tracked for 5 minutes
- Old data is automatically cleaned up (30 days for page analytics, 1 day for active users)

### Admin Dashboard Features
- Real-time updates every 10 seconds
- Live user count with animated indicators
- Page-by-page breakdown of activity
- Today's total page views
- Most popular pages ranking

## Privacy Considerations

The implementation follows privacy best practices:
- Uses Google Analytics 4 (GA4) which is privacy-focused
- Only tracks pageviews and basic navigation
- No personal information is collected beyond what GA4 does by default
- Users can disable tracking through browser settings

## Troubleshooting

### Analytics Not Working?
1. Check browser console for JavaScript errors
2. Ensure your Measurement ID is correct in both files
3. Verify Firebase is properly configured
4. Test with browser dev tools network tab to see if GA requests are being sent

### No Data in Admin Dashboard?
1. Make sure you have users visiting your site
2. Check Firebase console to see if data is being stored
3. Verify your Firebase security rules allow reading/writing to the analytics collections

### Real-Time Data Not Updating?
1. The dashboard updates every 10 seconds automatically
2. Check browser console for errors
3. Ensure your Firebase connection is working

## Next Steps

1. Set up conversion tracking for quote requests
2. Create custom events for important actions (form submissions, phone clicks)
3. Set up goals in Google Analytics for business metrics
4. Consider adding heat map tracking for detailed user behavior analysis
