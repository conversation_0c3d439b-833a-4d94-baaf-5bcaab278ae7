#!/bin/bash

# Ottawa Shine Solutions - Quick Setup Script
# This script helps you set up the environment for deployment

echo "🧹 Ottawa Shine Solutions - Quick Setup"
echo "======================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check Firebase project
print_info "Firebase Project: shinesolution-a372c"

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found"
    echo "Creating .env file from template..."
    cp .env.example .env
    print_status ".env file created"
    echo ""
    print_warning "IMPORTANT: You need to update .env with your Firebase configuration"
    echo "1. Go to Firebase Console: https://console.firebase.google.com/project/shinesolution-a372c"
    echo "2. Go to Project Settings > General > Your apps"
    echo "3. Copy the Firebase configuration values"
    echo "4. Update the .env file with your actual values"
    echo ""
else
    print_status ".env file already exists"
fi

# Show current Firebase project
echo ""
print_info "Current Firebase project configuration:"
if [ -f ".firebaserc" ]; then
    cat .firebaserc
    print_status "Firebase project configured: shinesolution-a372c"
else
    print_error "Firebase project not configured"
fi

echo ""
print_info "Your project URLs will be:"
echo "🌍 Website: https://shinesolution-a372c.web.app"
echo "🌍 Alternative: https://shinesolution-a372c.firebaseapp.com"
echo "🔥 Firebase Console: https://console.firebase.google.com/project/shinesolution-a372c"

echo ""
print_info "Next steps:"
echo "1. Update .env file with your Firebase configuration"
echo "2. Run: npm install"
echo "3. Run: ./deploy.sh"

echo ""
print_status "Setup complete! Ready for deployment configuration."
