#!/bin/bash

# Firestore Rules Deployment Script
# This script helps deploy and test Firestore security rules

echo "🔥 Ottawa Shine Solutions - Firestore Rules Deployment"
echo "=================================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "📦 Install it with: npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Please log in to Firebase first:"
    echo "📝 Run: firebase login"
    exit 1
fi

echo "✅ Firebase CLI is ready"

# Show current project
PROJECT=$(firebase use --json | jq -r '.result.current // "No project selected"')
echo "📂 Current project: $PROJECT"

if [ "$PROJECT" = "No project selected" ]; then
    echo "❌ No Firebase project selected."
    echo "📝 Run: firebase use <project-id>"
    exit 1
fi

echo ""
echo "🚀 Deploying Firestore security rules..."

# Deploy only Firestore rules
if firebase deploy --only firestore:rules; then
    echo "✅ Firestore rules deployed successfully!"
else
    echo "❌ Failed to deploy Firestore rules"
    exit 1
fi

echo ""
echo "🧪 Testing Rules (Manual Steps):"
echo "1. Open Firebase Console: https://console.firebase.google.com"
echo "2. Go to Firestore Database > Rules"
echo "3. Use the Rules Playground to test:"
echo ""
echo "Test Cases:"
echo "===================="
echo ""
echo "📝 Test 1: Quote Creation (Should SUCCEED)"
echo "Path: /databases/{database}/documents/quotes/test-quote-1"
echo "Method: create"
echo "Auth: null (unauthenticated)"
echo "Data:"
echo '{'
echo '  "name": "John Doe",'
echo '  "email": "<EMAIL>",'
echo '  "phone": "************",'
echo '  "serviceType": "residential",'
echo '  "propertySize": "medium",'
echo '  "frequency": "weekly",'
echo '  "message": "Need cleaning service",'
echo '  "timestamp": "2025-01-15T10:00:00Z",'
echo '  "status": "pending"'
echo '}'
echo ""
echo "📝 Test 2: Quote Read by Non-Admin (Should FAIL)"
echo "Path: /databases/{database}/documents/quotes/test-quote-1"
echo "Method: get"
echo "Auth: {uid: 'user123', token: {email: '<EMAIL>'}}"
echo ""
echo "📝 Test 3: Quote Read by Admin (Should SUCCEED)"
echo "Path: /databases/{database}/documents/quotes/test-quote-1"
echo "Method: get"
echo "Auth: {uid: 'admin123', token: {email: '<EMAIL>'}}"
echo ""
echo "📝 Test 4: Discount Code Read by Unauthenticated User (Should SUCCEED)"
echo "Path: /databases/{database}/documents/discountCodes/SAVE20"
echo "Method: get"
echo "Auth: null (unauthenticated)"
echo ""
echo "📝 Test 5: Contact Creation (Should SUCCEED)"
echo "Path: /databases/{database}/documents/contacts/test-contact-1"
echo "Method: create"
echo "Auth: null (unauthenticated)"
echo "Data:"
echo '{'
echo '  "name": "Jane Smith",'
echo '  "email": "<EMAIL>",'
echo '  "phone": "************",'
echo '  "subject": "Question about services",'
echo '  "message": "I have questions about your cleaning packages",'
echo '  "timestamp": "2025-01-15T10:00:00Z",'
echo '  "status": "new"'
echo '}'
echo ""
echo "🎉 Deployment complete! Test the rules using the Firebase Console."
echo "📊 Monitor rule usage in Firebase Console > Firestore > Usage tab"
