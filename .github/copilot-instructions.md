<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# Copilot Instructions for Cleaning Company Website

This is a React TypeScript project built with Vite for a professional cleaning company website called "Ottawa Shine Solutions".

## Project Structure
- Uses React 18 with TypeScript
- Styled with Tailwind CSS
- Icons from Lucide React
- Built with Vite for fast development and building

## Code Style Guidelines
- Use functional components with hooks
- Prefer TypeScript interfaces for props and data structures
- Use Tailwind CSS classes for styling
- Keep components modular and reusable
- Use semantic HTML elements
- Follow React best practices for state management

## Key Features
- Multi-page navigation (Home, Services, Quote, Contact)
- Interactive quote calculator with pricing
- Discount code system
- Contact forms with validation
- Responsive design
- Professional cleaning service focus

## Component Organization
- Keep components in separate files in src/components/
- Use hooks for shared logic in src/hooks/
- Place utilities and services in src/utils/ and src/services/
- Type definitions in src/types/

## Performance Optimizations
- Use React.lazy() and Suspense for code splitting
- Implement React.memo for frequently rendered components
- Use lazy loading for heavy components like forms and service cards
- Configure Vite for optimal chunk splitting
