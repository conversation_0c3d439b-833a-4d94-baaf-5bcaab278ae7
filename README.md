# Ottawa Shine Solutions - Professional Cleaning Services Website

A modern, responsive React TypeScript application built with Vite for a professional cleaning company. Features include service showcase, interactive quote calculator, discount code system, and contact forms.

## Features

- **Multi-page Navigation**: Home, Services, Quote, and Contact pages
- **Interactive Quote Calculator**: Real-time pricing with customizable options
- **Discount Code System**: Apply and validate discount codes
- **Responsive Design**: Optimized for all devices
- **Modern UI**: Built with Tailwind CSS and Lucide React icons
- **Form Validation**: Contact and quote forms with validation
- **TypeScript**: Full type safety throughout the application

## Tech Stack

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **Firebase Integration** for real-time data storage
- **Environment Configuration** for secure API key management

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Navigation.tsx
│   ├── ServiceCard.tsx
│   ├── DiscountCodeInput.tsx
│   ├── FormInput.tsx
│   └── FormSelect.tsx
├── pages/              # Page components
│   ├── HomePage.tsx
│   ├── ServicesPage.tsx
│   ├── QuotePage.tsx
│   └── ContactPage.tsx
├── hooks/              # Custom React hooks
│   └── useDiscountCode.ts
├── services/           # External services
│   └── firebase.ts
├── types/              # TypeScript definitions
│   └── index.ts
├── utils/              # Utility functions
│   ├── pricing.ts
│   ├── services.ts
│   └── performance.ts  # Performance monitoring utilities
└── App.tsx            # Main application component
```

## Getting Started

### Prerequisites
- Node.js 18+ and npm
- Firebase project with Firestore enabled

### Installation

1. **Clone the repository and install dependencies**:
   ```bash
   npm install
   ```

2. **Set up Firebase configuration**:
   - Copy `.env.example` to `.env`
   - Replace the placeholder values with your actual Firebase configuration:
   ```bash
   cp .env.example .env
   ```
   - Edit `.env` with your Firebase project details

3. **Firebase Setup**:
   - Create these collections in your Firestore database:
     - `quotes` - For storing quote requests
     - `contacts` - For storing contact messages
     - `discountCodes` - For storing discount codes (format: `{percentage: number, active: boolean}`)

4. **Start the development server**:
   ```bash
   npm run dev
   ```

5. **Build for production**:
   ```bash
   npm run build
   ```

## Available Services

- **House Cleaning**: Residential cleaning services starting at $80
- **Office Cleaning**: Commercial cleaning services starting at $120  
- **Hotel Cleaning**: Specialized hospitality cleaning starting at $200

## Discount Codes (Demo)

Try these discount codes in the quote form:
- `SAVE10` - 10% discount
- `SAVE20` - 20% discount
- `FIRST15` - 15% discount

## Development

The application uses a modular architecture with:
- Functional components with hooks
- TypeScript interfaces for type safety
- Tailwind CSS for styling
- Custom hooks for shared logic
- Mock services for data persistence

## License

MIT License
